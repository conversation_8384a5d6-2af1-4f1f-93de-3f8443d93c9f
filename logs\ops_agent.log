2025-08-28 09:10:25,124 - ops_agent - WARNING - 邮件配置不完整，请检查环境变量 EMAIL_SENDER 和 EMAIL_PASSWORD
2025-08-28 09:10:25,125 - ops_agent - INFO - 邮件发送器初始化完成，SMTP: smtp.exmail.qq.com:465
2025-08-28 09:10:32,966 - ops_agent - WARNING - 邮件配置不完整，请检查环境变量 EMAIL_SENDER 和 EMAIL_PASSWORD
2025-08-28 09:10:32,966 - ops_agent - INFO - 邮件发送器初始化完成，SMTP: smtp.exmail.qq.com:465
2025-08-28 09:10:32,970 - ops_agent - INFO - GitHub趋势代理初始化完成
2025-08-28 09:23:39,596 - ops_agent - WARNING - 邮件配置不完整，请检查环境变量 EMAIL_SENDER 和 EMAIL_PASSWORD
2025-08-28 09:23:39,598 - ops_agent - INFO - 邮件发送器初始化完成，SMTP: smtp.exmail.qq.com:465
2025-08-28 09:23:39,606 - ops_agent - INFO - OPS Agent基础框架初始化完成
2025-08-28 09:24:24,579 - ops_agent - INFO - 工具执行开始 [__main__.invoke_agent] - 参数: {}
2025-08-28 09:24:24,580 - ops_agent - INFO - 开始处理任务: 获取github今天最流行的项目
2025-08-28 09:24:24,580 - ops_agent - INFO - 工具执行开始 [__main__.get_tools] - 参数: {}
2025-08-28 09:24:24,586 - ops_agent - INFO - 已加载工具列表: ['example_operation', 'github_trending_analysis']
2025-08-28 09:24:24,586 - ops_agent - INFO - 工具执行成功 [__main__.get_tools] - 耗时: 0.01秒
2025-08-28 09:24:24,586 - ops_agent - INFO - 创建 agent_executor
2025-08-28 09:24:24,605 - ops_agent - INFO - 开始执行 agent
2025-08-28 09:24:25,801 - httpx - INFO - HTTP Request: POST https://apiproxy.inhand.ai/v1/chat/completions "HTTP/1.1 403 Forbidden"
2025-08-28 09:24:25,804 - ops_agent - ERROR - 执行agent时发生错误: <html>

<head><title>403 Forbidden</title></head>

<body>

<center><h1>403 Forbidden</h1></center>

<hr><center>nginx/1.26.3</center>

</body>

</html>
Traceback (most recent call last):
  File "D:\fanpeng\github_trending_agent\ops_agent.py", line 236, in invoke_agent
    response = await agent_executor.ainvoke(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\pregel\__init__.py", line 2788, in ainvoke
    async for chunk in self.astream(
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\pregel\__init__.py", line 2655, in astream
    async for _ in runner.atick(
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\pregel\runner.py", line 294, in atick
    await arun_with_retry(
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\pregel\retry.py", line 136, in arun_with_retry
    return await task.proc.ainvoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\utils\runnable.py", line 672, in ainvoke
    input = await asyncio.create_task(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\utils\runnable.py", line 431, in ainvoke
    ret = await asyncio.create_task(coro, context=context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\prebuilt\chat_agent_executor.py", line 523, in acall_model
    response = cast(AIMessage, await model_runnable.ainvoke(state, config))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\runnables\base.py", line 3089, in ainvoke
    input_ = await coro_with_context(part(), context, create_task=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\runnables\base.py", line 5444, in ainvoke
    return await self.bound.ainvoke(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\language_models\chat_models.py", line 394, in ainvoke
    llm_result = await self.agenerate_prompt(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\language_models\chat_models.py", line 968, in agenerate_prompt
    return await self.agenerate(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\language_models\chat_models.py", line 926, in agenerate
    raise exceptions[0]
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1094, in _agenerate_with_cache
    result = await self._agenerate(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_openai\chat_models\base.py", line 1281, in _agenerate
    response = await self.async_client.create(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\openai\resources\chat\completions\completions.py", line 2028, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\openai\_base_client.py", line 1762, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\openai\_base_client.py", line 1562, in request
    raise self._make_status_error_from_response(err.response) from None
openai.PermissionDeniedError: <html>

<head><title>403 Forbidden</title></head>

<body>

<center><h1>403 Forbidden</h1></center>

<hr><center>nginx/1.26.3</center>

</body>

</html>
During task with name 'agent' and id '4c911006-5064-0a08-b108-db8fe97d66d8'
2025-08-28 09:24:25,822 - ops_agent - INFO - 工具执行成功 [__main__.invoke_agent] - 耗时: 1.24秒
2025-08-28 09:24:37,177 - ops_agent - INFO - 工具执行开始 [__main__.invoke_agent] - 参数: {}
2025-08-28 09:24:37,177 - ops_agent - INFO - 开始处理任务: 获取github今天最流行的项目
2025-08-28 09:24:37,177 - ops_agent - INFO - 工具执行开始 [__main__.get_tools] - 参数: {}
2025-08-28 09:24:37,179 - ops_agent - INFO - 已加载工具列表: ['example_operation', 'github_trending_analysis']
2025-08-28 09:24:37,179 - ops_agent - INFO - 工具执行成功 [__main__.get_tools] - 耗时: 0.00秒
2025-08-28 09:24:37,179 - ops_agent - INFO - 创建 agent_executor
2025-08-28 09:24:37,190 - ops_agent - INFO - 开始执行 agent
2025-08-28 09:24:38,396 - httpx - INFO - HTTP Request: POST https://apiproxy.inhand.ai/v1/chat/completions "HTTP/1.1 403 Forbidden"
2025-08-28 09:24:38,398 - ops_agent - ERROR - 执行agent时发生错误: <html>

<head><title>403 Forbidden</title></head>

<body>

<center><h1>403 Forbidden</h1></center>

<hr><center>nginx/1.26.3</center>

</body>

</html>
Traceback (most recent call last):
  File "D:\fanpeng\github_trending_agent\ops_agent.py", line 236, in invoke_agent
    response = await agent_executor.ainvoke(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\pregel\__init__.py", line 2788, in ainvoke
    async for chunk in self.astream(
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\pregel\__init__.py", line 2655, in astream
    async for _ in runner.atick(
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\pregel\runner.py", line 294, in atick
    await arun_with_retry(
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\pregel\retry.py", line 136, in arun_with_retry
    return await task.proc.ainvoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\utils\runnable.py", line 672, in ainvoke
    input = await asyncio.create_task(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\utils\runnable.py", line 431, in ainvoke
    ret = await asyncio.create_task(coro, context=context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langgraph\prebuilt\chat_agent_executor.py", line 523, in acall_model
    response = cast(AIMessage, await model_runnable.ainvoke(state, config))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\runnables\base.py", line 3089, in ainvoke
    input_ = await coro_with_context(part(), context, create_task=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\runnables\base.py", line 5444, in ainvoke
    return await self.bound.ainvoke(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\language_models\chat_models.py", line 394, in ainvoke
    llm_result = await self.agenerate_prompt(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\language_models\chat_models.py", line 968, in agenerate_prompt
    return await self.agenerate(
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\language_models\chat_models.py", line 926, in agenerate
    raise exceptions[0]
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1094, in _agenerate_with_cache
    result = await self._agenerate(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\langchain_openai\chat_models\base.py", line 1281, in _agenerate
    response = await self.async_client.create(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\openai\resources\chat\completions\completions.py", line 2028, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\openai\_base_client.py", line 1762, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\fanpeng\github_trending_agent\.venv\Lib\site-packages\openai\_base_client.py", line 1562, in request
    raise self._make_status_error_from_response(err.response) from None
openai.PermissionDeniedError: <html>

<head><title>403 Forbidden</title></head>

<body>

<center><h1>403 Forbidden</h1></center>

<hr><center>nginx/1.26.3</center>

</body>

</html>
During task with name 'agent' and id 'a33ea30e-ddb7-a1bb-a77b-23da05d8fd26'
2025-08-28 09:24:38,405 - ops_agent - INFO - 工具执行成功 [__main__.invoke_agent] - 耗时: 1.23秒
