# OpenAI API 配置 (必需)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_BASE_URL=https://api.openai.com/v1

# GitHub API 配置 (可选，用于提高API限制)
GITHUB_TOKEN=your_github_token_here

# 邮件配置 (可选)
EMAIL_SMTP_SERVER=smtp.exmail.qq.com
EMAIL_SMTP_PORT=465
EMAIL_SENDER=<EMAIL>
EMAIL_PASSWORD=wzqzqzqzqzq
EMAIL_RECEIVERS=<EMAIL>,<EMAIL>

# 其他配置
RECURSION_LIMIT=100
(ops-agent) PS D:\fanpeng\github_trending_agent>  python github_trending_cli.py run-video daily
2025-09-01 17:31:12,767 - ops_agent - INFO - 邮件发送器初始化完成，SMTP: smtp.exmail.qq.com:465
Traceback (most recent call last):
  File "D:\fanpeng\github_trending_agent\github_trending_cli.py", line 24, in <module>
    from agents.video_publish_agent import VideoPublishAgent
  File "D:\fanpeng\github_trending_agent\agents\video_publish_agent.py", line 16, in <module>
    from moviepy.editor import AudioFileClip
ModuleNotFoundError: No module named 'moviepy.editor'
(ops-agent) PS D:\fanpeng\github_trending_agent>  2025-09-01 17:31:12,767 - ops_agent - INFO - 邮件发送器初始化完成，SMTP: smtp.exmail.qq.com:465
Traceback (most recent call last):
  File "D:\fanpeng\github_trending_agent\github_trending_cli.py", line 24, in <module>
    from agents.video_publish_agent import VideoPublishAgent
  File "D:\fanpeng\github_trending_agent\agents\video_publish_agent.py", line 16, in <module>
    from moviepy.editor import AudioFileClip
ModuleNotFoundError: No module named 'moviepy.editor'