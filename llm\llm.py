# -*- coding: utf-8 -*-

"""
File: llm
Author: <PERSON><PERSON>
Date: 2025/6/17
"""
import os
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

load_dotenv()

RECURSION_LIMIT = int(os.getenv("RECURSION_LIMIT", 100))

# 按照官方文档初始化Langfuse
def get_langfuse_handler():
    """获取Langfuse回调处理器 - 按照官方文档标准方式"""
    try:
        # 从环境变量读取配置
        public_key = os.getenv("LANGFUSE_PUBLIC_KEY")
        secret_key = os.getenv("LANGFUSE_SECRET_KEY")
        host = os.getenv("LANGFUSE_HOST")

        if all([public_key, secret_key, host]):
            print(f"🔍 Langfuse config:")
            print(f"  - Public Key: {public_key[:10]}...")
            print(f"  - Secret Key: {secret_key[:10]}...")
            print(f"  - Host: {host}")

            from langfuse.langchain import CallbackHandler
            # 按照官方文档，直接初始化（会自动读取环境变量）
            langfuse_handler = CallbackHandler()
            print("✅ Langfuse handler initialized successfully")
            return langfuse_handler
        else:
            print("❌ Langfuse environment variables not set, skipping tracing")
            return None

    except ImportError:
        print("❌ Langfuse not installed, skipping tracing")
        return None
    except Exception as e:
        print(f"❌ Failed to initialize Langfuse handler: {e}")
        return None

def get_langfuse_callbacks():
    """获取Langfuse回调列表"""
    handler = get_langfuse_handler()
    return [handler] if handler else []

# 初始化LLM模型
llm = ChatOpenAI(model=os.getenv('OPENAI_MODEL'), api_key=os.getenv('OPENAI_API_KEY'))  # 处理复杂业务，逻辑强

