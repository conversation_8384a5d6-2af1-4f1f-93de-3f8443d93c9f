# -*- coding: utf-8 -*-

"""
File: llm.py
Author: AI Assistant
Date: 2025/8/27

LLM配置模块
"""
import os
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

load_dotenv()

RECURSION_LIMIT = int(os.getenv("RECURSION_LIMIT", 100))

# 初始化LLM模型
llm = ChatOpenAI(
    model=os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo'),
    api_key=os.getenv('OPENAI_API_KEY'),
    base_url=os.getenv('OPENAI_BASE_URL')
)

