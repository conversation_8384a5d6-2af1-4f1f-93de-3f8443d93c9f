#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
证书API客户端
用于从CMDB系统获取证书和平台域名映射数据
"""

import http.client
import json
import ssl
import urllib.parse
import logging
from typing import Dict, List, Optional, Any

# 配置日志
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)


class CertificateAPIClient:
    """证书API客户端"""
    
    def __init__(self, base_url: str = "demo.inhand.online", timeout: int = 30):
        """
        初始化API客户端
        
        Args:
            base_url: API基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url
        self.timeout = timeout
        self.api_params = {
            'bk_app_code': 'cmdb',
            'bk_app_secret': 'eee5b34e-fc09-11ea-9e6a-00163e105ceb',
            'model_code': 'ZhengShu',
            'username': 'admin',
            'per_page': 100
        }
        
    def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        发起HTTP请求
        
        Args:
            endpoint: API端点
            params: 请求参数
            
        Returns:
            API响应数据
            
        Raises:
            Exception: 请求失败时抛出异常
        """
        try:
            # 合并参数
            all_params = {**self.api_params}
            if params:
                all_params.update(params)
            
            # 构建查询字符串
            query_string = urllib.parse.urlencode(all_params)
            full_path = f"{endpoint}?{query_string}"
            
            # 创建HTTPS连接
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            conn = http.client.HTTPSConnection(self.base_url, timeout=self.timeout, context=context)
            
            headers = {
                'User-Agent': 'OPS-Agent/1.0.0',
                'Accept': 'application/json',
                'Host': self.base_url,
                'Connection': 'keep-alive'
            }
            
            logger.info(f"请求证书API: {self.base_url}{full_path}")
            conn.request("GET", full_path, headers=headers)
            
            response = conn.getresponse()
            data = response.read()
            conn.close()
            
            if response.status != 200:
                raise Exception(f"HTTP {response.status}: {response.reason}")
            
            # 解析JSON响应
            result = json.loads(data.decode('utf-8'))
            
            if not result.get('result', False):
                raise Exception(f"API返回错误: {result.get('message', '未知错误')}")
            
            logger.info(f"成功获取证书数据，共 {result['data']['total']} 条记录")
            return result
            
        except Exception as e:
            logger.error(f"证书API请求失败: {str(e)}")
            raise
    
    def get_certificates(self, page: int = 1, per_page: int = 100) -> List[Dict[str, Any]]:
        """
        获取证书列表
        
        Args:
            page: 页码
            per_page: 每页数量
            
        Returns:
            证书数据列表
        """
        try:
            params = {
                'current': page,
                'per_page': per_page
            }
            
            response = self._make_request('/api/c/compapi/cmdb/model_data_get/', params)
            return response['data']['data']
            
        except Exception as e:
            logger.error(f"获取证书列表失败: {str(e)}")
            return []
    
    def get_all_certificates(self) -> List[Dict[str, Any]]:
        """
        获取所有证书数据（自动分页）
        
        Returns:
            所有证书数据列表
        """
        all_certificates = []
        page = 1
        per_page = 100
        
        try:
            while True:
                params = {
                    'current': page,
                    'per_page': per_page
                }
                
                response = self._make_request('/api/c/compapi/cmdb/model_data_get/', params)
                certificates = response['data']['data']
                
                if not certificates:
                    break
                
                all_certificates.extend(certificates)
                
                # 检查是否还有更多数据
                total = response['data']['total']
                if len(all_certificates) >= total:
                    break
                
                page += 1
            
            logger.info(f"成功获取所有证书数据，共 {len(all_certificates)} 条")
            return all_certificates
            
        except Exception as e:
            logger.error(f"获取所有证书数据失败: {str(e)}")
            return []
    
    def build_platform_mapping(self) -> Dict[str, Any]:
        """
        构建平台域名映射数据

        Returns:
            平台映射数据字典
        """
        certificates = self.get_all_certificates()
        if not certificates:
            logger.warning("未获取到证书数据，返回空映射")
            return {}

        platform_mapping = {
            'platforms': [],
            'unsupported_platforms': [],  # 不支持的平台
            'dns_providers': {},
            'total_count': len(certificates),
            'supported_count': 0,
            'unsupported_count': 0,
            'last_updated': None
        }
        
        dns_provider_map = {
            '阿里云': 'aliyun',
            'AWS Route53': 'aws',
            'Cloudflare': 'cloudflare'
        }
        
        for cert in certificates:
            try:
                cert_data = cert.get('data', {})

                # 基础平台信息
                platform_info = {
                    'code': cert.get('code'),
                    'visible_name': cert_data.get('ZhengShu_VISIBLE_NAME', ''),
                    'cert_name': cert_data.get('ZhengShu_name', ''),
                    'domains': cert_data.get('ZhengShu_subjectname', '').split(', ') if cert_data.get('ZhengShu_subjectname') else [],
                    'dns_server': cert_data.get('ZhengShu_DNSServer', ''),
                    'dns_provider': dns_provider_map.get(cert_data.get('ZhengShu_DNSServer', ''), 'unknown'),
                    'verify_type': cert_data.get('ZhengShu_verfity_type', 'txt'),
                    'issuer': cert_data.get('ZhengShu_QianFaJiGou', 'Letsencrypt'),
                    'purchase_method': cert_data.get('ZhengShu_CaiGouFangShi', ''),
                    'price': cert_data.get('ZhengShu_price', 0)
                }

                # 检查是否支持自动签发
                issuer = cert_data.get('ZhengShu_QianFaJiGou', '')
                verify_type = cert_data.get('ZhengShu_verfity_type', '')

                is_supported = (issuer == 'Letsencrypt' and verify_type == 'txt')
                platform_info['is_supported'] = is_supported

                if is_supported:
                    # 支持的平台
                    platform_mapping['platforms'].append(platform_info)
                    platform_mapping['supported_count'] += 1

                    # 统计DNS提供商
                    dns_provider = platform_info['dns_provider']
                    if dns_provider not in platform_mapping['dns_providers']:
                        platform_mapping['dns_providers'][dns_provider] = 0
                    platform_mapping['dns_providers'][dns_provider] += 1
                else:
                    # 不支持的平台，记录原因
                    unsupported_reasons = []
                    if issuer != 'Letsencrypt':
                        unsupported_reasons.append(f"签发机构为'{issuer}'，仅支持Letsencrypt")
                    if verify_type != 'txt':
                        unsupported_reasons.append(f"验证方式为'{verify_type}'，仅支持txt验证")

                    platform_info['unsupported_reasons'] = unsupported_reasons
                    platform_mapping['unsupported_platforms'].append(platform_info)
                    platform_mapping['unsupported_count'] += 1

            except Exception as e:
                logger.warning(f"处理证书数据失败: {cert}, 错误: {str(e)}")
                continue
        
        logger.info(f"构建平台映射完成，共 {platform_mapping['total_count']} 个平台")
        logger.info(f"支持自动签发: {platform_mapping['supported_count']} 个平台")
        logger.info(f"不支持自动签发: {platform_mapping['unsupported_count']} 个平台")
        logger.info(f"DNS提供商分布: {platform_mapping['dns_providers']}")

        return platform_mapping
    
    def search_platforms(self, query: str, include_unsupported: bool = False) -> Dict[str, Any]:
        """
        搜索匹配的平台

        Args:
            query: 搜索关键词
            include_unsupported: 是否包含不支持的平台

        Returns:
            搜索结果字典，包含支持和不支持的平台
        """
        platform_mapping = self.build_platform_mapping()
        supported_platforms = platform_mapping.get('platforms', [])
        unsupported_platforms = platform_mapping.get('unsupported_platforms', [])

        result = {
            'supported': [],
            'unsupported': [],
            'total_supported': 0,
            'total_unsupported': 0
        }

        if not query:
            result['supported'] = supported_platforms
            if include_unsupported:
                result['unsupported'] = unsupported_platforms
            result['total_supported'] = len(supported_platforms)
            result['total_unsupported'] = len(unsupported_platforms)
            return result

        query_lower = query.lower()

        # 搜索支持的平台
        for platform in supported_platforms:
            if self._platform_matches_query(platform, query_lower):
                result['supported'].append(platform)

        # 搜索不支持的平台（如果需要）
        if include_unsupported:
            for platform in unsupported_platforms:
                if self._platform_matches_query(platform, query_lower):
                    result['unsupported'].append(platform)

        result['total_supported'] = len(result['supported'])
        result['total_unsupported'] = len(result['unsupported'])

        logger.info(f"搜索 '{query}' 找到 {result['total_supported']} 个支持的平台，{result['total_unsupported']} 个不支持的平台")
        return result

    def _platform_matches_query(self, platform: Dict[str, Any], query_lower: str) -> bool:
        """检查平台是否匹配搜索关键词"""
        visible_name = platform.get('visible_name', '').lower()
        cert_name = platform.get('cert_name', '').lower()
        domains = [d.lower() for d in platform.get('domains', [])]

        return (query_lower in visible_name or
                query_lower in cert_name or
                any(query_lower in domain for domain in domains))


def test_api_client():
    """测试API客户端"""
    try:
        client = CertificateAPIClient()
        
        # 测试获取证书数据
        print("🔍 测试获取证书数据...")
        certificates = client.get_certificates(page=1, per_page=5)
        print(f"获取到 {len(certificates)} 条证书数据")
        
        if certificates:
            print("\n📋 示例证书数据:")
            for i, cert in enumerate(certificates[:2]):
                cert_data = cert.get('data', {})
                print(f"{i+1}. {cert_data.get('ZhengShu_VISIBLE_NAME')} - {cert_data.get('ZhengShu_name')}")
        
        # 测试构建平台映射
        print("\n🗺️ 测试构建平台映射...")
        mapping = client.build_platform_mapping()
        print(f"平台总数: {mapping.get('total_count', 0)}")
        print(f"支持自动签发: {mapping.get('supported_count', 0)} 个")
        print(f"不支持自动签发: {mapping.get('unsupported_count', 0)} 个")
        print(f"DNS提供商分布: {mapping.get('dns_providers', {})}")

        # 测试搜索功能
        print("\n🔍 测试搜索功能...")
        results = client.search_platforms("研发支撑", include_unsupported=True)
        print(f"搜索'研发支撑'找到 {results['total_supported']} 个支持的平台，{results['total_unsupported']} 个不支持的平台")

        # 显示不支持的平台示例
        if results['unsupported']:
            print("\n❌ 不支持的平台示例:")
            for platform in results['unsupported'][:2]:
                print(f"- {platform['visible_name']}: {', '.join(platform.get('unsupported_reasons', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


if __name__ == '__main__':
    test_api_client()
