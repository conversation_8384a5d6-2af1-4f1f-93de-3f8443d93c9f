#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Zabbix MCP客户端

基于官方MCP Python SDK实现的Zabbix客户端，用于连接和管理zabbix-mcp-server进程。
"""

import json
from pathlib import Path
from typing import Dict, Any, List
from utils.logging_config import logger

# 导入官方MCP SDK
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


class ZabbixMCPClient:
    """
    基于官方MCP SDK的Zabbix客户端。

    该客户端负责根据需要自动启动一个zabbix-mcp-server子进程，
    并通过标准输入输出（STDIO）与其进行通信。
    它提供了两个核心方法：
    1. call_tool: 调用服务器上的一个具体工具。
    2. get_available_tools: 获取服务器提供的所有可用工具的列表。
    """

    def __init__(self):
        """初始化Zabbix MCP客户端"""
        # 动态计算出zabbix-mcp-server的绝对路径，以确保可移植性
        self.mcp_server_dir = Path(__file__).parent.parent.parent / "mcp-server" / "zabbix-mcp-server"
        logger.info(f"Zabbix MCP客户端初始化完成，服务器目录: {self.mcp_server_dir}")

    async def _execute_with_session(self, operation) -> Any:
        """
        创建一个MCP会话来执行给定的操作。

        这个方法会启动一个zabbix-mcp-server子进程，并与其建立一个MCP会话。
        操作完成后，会自动关闭会话并终止子进程。

        Args:
            operation: 一个接受session对象作为参数的异步函数。

        Returns:
            操作函数的返回值。
        """
        # 配置启动zabbix-mcp-server的命令。
        # 服务器的配置（如Zabbix URL和Token）应由其自身的.env文件管理，
        # 客户端不注入特定环境配置，以保证职责分离。
        server_params = StdioServerParameters(
            command="uv",
            args=["run", "python", "src/zabbix_mcp_server.py"],
            cwd=str(self.mcp_server_dir)
        )

        try:
            # 使用官方SDK启动服务器子进程并建立连接
            async with stdio_client(server_params) as (read_stream, write_stream):
                async with ClientSession(read_stream, write_stream) as session:
                    # 初始化会话
                    await session.initialize()
                    # 执行传入的具体操作 (如调用工具、列出工具)
                    return await operation(session)
        except Exception as e:
            logger.error(f"与MCP服务器会话执行失败: {e}", exc_info=True)
            # 将异常包装后重新抛出，以便上层调用者可以捕获详细错误
            raise ConnectionError(f"无法与zabbix-mcp-server建立会话: {e}") from e

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Any:
        """
        调用在MCP服务器上注册的特定工具。

        Args:
            tool_name: 要调用的工具名称。
            arguments: 调用工具时需要传递的参数字典。

        Returns:
            工具执行成功时返回结果，失败时返回包含错误信息的字典。
        """
        if arguments is None:
            arguments = {}

        async def operation(session: ClientSession):
            logger.debug(f"调用MCP工具: {tool_name}, 参数: {arguments}")
            result = await session.call_tool(tool_name, arguments)

            # 处理和解析返回结果
            if hasattr(result, 'content') and result.content:
                content_text = "".join(item.text for item in result.content if hasattr(item, 'text'))
                if content_text:
                    try:
                        return json.loads(content_text)
                    except json.JSONDecodeError:
                        # 如果不是有效的JSON，返回原始文本
                        return {"result": content_text}
            
            return {"result": "No content returned from tool."}

        return await self._execute_with_session(operation)

    async def diagnose_connection(self) -> Dict[str, Any]:
        """诊断 Zabbix 连接问题"""
        try:
            logger.info("🔍 开始诊断 Zabbix MCP 连接...")

            # 测试最基本的 API 调用
            result = await self.call_tool("user_get", {"output": "extend"})

            if isinstance(result, dict) and "result" in result and "Error calling tool" in str(result.get("result", "")):
                return {
                    "status": "failed",
                    "error": result.get("result"),
                    "suggestion": "检查 Zabbix API Token 权限和服务器配置"
                }

            return {
                "status": "success",
                "message": "连接正常",
                "data": result
            }
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "suggestion": "检查 MCP 服务器是否正常运行"
            }

    async def test_basic_operations(self) -> Dict[str, Any]:
        """测试基本的 Zabbix 操作"""
        tests = {}

        # 测试1: 获取用户信息
        try:
            user_result = await self.call_tool("user_get", {})
            tests["user_get"] = {"status": "success", "data": user_result}
        except Exception as e:
            tests["user_get"] = {"status": "failed", "error": str(e)}

        # 测试2: 获取主机组
        try:
            group_result = await self.call_tool("hostgroup_get", {})
            tests["hostgroup_get"] = {"status": "success", "data": group_result}
        except Exception as e:
            tests["hostgroup_get"] = {"status": "failed", "error": str(e)}

        # 测试3: 获取主机
        try:
            host_result = await self.call_tool("host_get", {})
            tests["host_get"] = {"status": "success", "data": host_result}
        except Exception as e:
            tests["host_get"] = {"status": "failed", "error": str(e)}

        return tests

    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """
        获取MCP服务器上所有可用的工具列表。

        Returns:
            一个包含工具信息的字典列表，获取失败时返回空列表。
        """
        async def operation(session: ClientSession):
            tools_result = await session.list_tools()
            
            # 处理不同的返回格式
            tools_list = tools_result.tools if hasattr(tools_result, 'tools') else tools_result

            # 将工具信息统一转换为标准的字典格式
            formatted_tools = []
            for tool in tools_list:
                if hasattr(tool, 'name'):
                    formatted_tools.append({
                        "name": tool.name,
                        "description": getattr(tool, 'description', ''),
                        "schema": getattr(tool, 'inputSchema', {})
                    })
                elif isinstance(tool, dict):
                    formatted_tools.append(tool)
            return formatted_tools

        try:
            return await self._execute_with_session(operation)
        except Exception as e:
            logger.error(f"从MCP服务器获取可用工具列表失败: {e}")
            return []
