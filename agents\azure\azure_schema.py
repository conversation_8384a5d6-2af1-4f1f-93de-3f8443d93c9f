# -*- coding: utf-8 -*-

"""
File: azure_schema
Author: Pane Li
Date: 2025/6/17
"""
from typing import Dict, Optional
from pydantic import BaseModel, Field

# 定义参数模型
class GetUsersSchema(BaseModel):
    filter_criteria: Optional[str] = Field(None, description="过滤条件")


class GetUserByIdSchema(BaseModel):
    user_id: str = Field(..., description="用户ID")


class GetUserByEmailSchema(BaseModel):
    email: str = Field(..., description="用户电子邮件地址")


class GetGroupsSchema(BaseModel):
    filter_criteria: Optional[str] = Field(None, description="过滤条件")


class GetGroupByNameSchema(BaseModel):
    group_name: str = Field(..., description="组名称")


class GetGroupMembersSchema(BaseModel):
    group_id: str = Field(..., description="组ID")


class AddUserSchema(BaseModel):
    display_name: str = Field(..., description="用户显示名称")
    email: str = Field(..., description="用户邮箱地址")
    phone_number: str = Field(..., description="用户手机号码")
    password: Optional[str] = Field(None, description="用户初始密码，如果不提供将生成随机密码")


class DeleteUserSchema(BaseModel):
    user_id_or_email: str = Field(..., description="用户ID或电子邮件地址")


class AddGroupSchema(BaseModel):
    display_name: str = Field(..., description="组显示名称")
    description: Optional[str] = Field(None, description="组描述")
    mail_nickname: Optional[str] = Field(None, description="邮件昵称")
    mail_enabled: bool = Field(False, description="是否启用邮件功能")
    security_enabled: bool = Field(True, description="是否为安全组")


class DeleteGroupSchema(BaseModel):
    group_id_or_name: str = Field(..., description="组ID或组名称")


class AddUserToGroupSchema(BaseModel):
    user_id_or_email: str = Field(..., description="用户ID或电子邮件地址")
    group_id_or_name: str = Field(..., description="组ID或组名称")


class RemoveUserFromGroupSchema(BaseModel):
    user_id_or_email: str = Field(..., description="用户ID或电子邮件地址")
    group_id_or_name: str = Field(..., description="组ID或组名称")


class UpdateUserSchema(BaseModel):
    user_id_or_email: str = Field(..., description="用户ID或电子邮件地址")
    update_data: Dict = Field(..., description="要更新的用户数据")


class ResetUserPasswordSchema(BaseModel):
    user_id_or_email: str = Field(..., description="用户ID或电子邮件地址")
    new_password: Optional[str] = Field(None, description="新密码，如果不提供则生成随机密码")
    force_change_password: bool = Field(True, description="是否强制用户下次登录时更改密码")


class GetUserGroupsSchema(BaseModel):
    user_id_or_email: str = Field(..., description="用户ID或电子邮件地址")


class CheckUserInGroupSchema(BaseModel):
    user_id_or_email: str = Field(..., description="用户ID或电子邮件地址")
    group_id_or_name: str = Field(..., description="组ID或组名称")


class DisableUserSchema(BaseModel):
    user_id_or_email: str = Field(..., description="用户ID或电子邮件地址")


class EnableUserSchema(BaseModel):
    user_id_or_email: str = Field(..., description="用户ID或电子邮件地址")


class HandleResignationSchema(BaseModel):
    user_name: str = Field(..., description="员工姓名")


class GetDisabledUsersSchema(BaseModel):
    filter_criteria: Optional[str] = Field(None, description="额外的过滤条件")