# -*- coding: utf-8 -*-

"""
SSL证书SSH推送工具

将本地data目录下的证书推送到远程主机
"""

import os
import logging
import paramiko
import stat
from typing import Dict, Any, List
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class SSLCertificatePusher:
    """SSL证书SSH推送器"""
    
    def __init__(self):
        self.config = {
            'ssh_host': os.getenv('SSH_HOST', '************'),
            'ssh_username': os.getenv('SSH_USERNAME', 'ops-agent'),
            'ssh_password': os.getenv('SSH_PASSWORD', 'ops-agent'),
            'ssh_remote_path': os.getenv('SSH_REMOTE_PATH', '/mnt/ops/ssl'),
            'ssh_port': int(os.getenv('SSH_PORT', '22')),
            'local_cert_path': os.getenv('CERT_OUTPUT_PATH', './data/certificates'),
        }
    
    def push_certificate(self, domain: str) -> Dict[str, Any]:
        """推送指定域名的证书到远程主机"""
        try:
            # 检查本地证书是否存在
            local_domain_path = os.path.join(self.config['local_cert_path'], domain)
            if not os.path.exists(local_domain_path):
                return {
                    "success": False,
                    "message": f"本地证书目录不存在: {local_domain_path}",
                    "domain": domain
                }
            
            # 获取SSH配置
            ssh_host = self.config['ssh_host']
            ssh_username = self.config['ssh_username']
            ssh_password = self.config['ssh_password']
            ssh_port = self.config['ssh_port']
            remote_path = self.config['ssh_remote_path']
            
            logger.info(f"开始推送证书 {domain} 到远程主机: {ssh_username}@{ssh_host}:{ssh_port}")
            
            # 创建SSH客户端
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接到远程主机
            ssh_client.connect(
                hostname=ssh_host,
                port=ssh_port,
                username=ssh_username,
                password=ssh_password,
                timeout=30
            )
            
            # 创建SFTP客户端
            sftp = ssh_client.open_sftp()
            
            # 确保远程目录存在
            remote_domain_path = f"{remote_path}/{domain}"
            try:
                sftp.mkdir(remote_path)
                logger.debug(f"创建远程目录: {remote_path}")
            except IOError:
                pass  # 目录可能已存在
            
            try:
                sftp.mkdir(remote_domain_path)
                logger.debug(f"创建远程域名目录: {remote_domain_path}")
            except IOError:
                pass  # 目录可能已存在
            
            # 推送证书文件
            cert_files = ['cert.pem', 'privkey.pem', 'chain.pem', 'fullchain.pem']
            pushed_files = []
            
            for cert_file in cert_files:
                local_file = os.path.join(local_domain_path, cert_file)
                remote_file = f"{remote_domain_path}/{cert_file}"
                
                if os.path.exists(local_file):
                    sftp.put(local_file, remote_file)
                    # 设置文件权限（744 - 所有者可读写执行，组和其他用户可读）
                    sftp.chmod(remote_file, stat.S_IRUSR | stat.S_IWUSR | stat.S_IXUSR | stat.S_IRGRP | stat.S_IROTH)
                    pushed_files.append(cert_file)
                    logger.info(f"推送证书文件: {cert_file} -> {remote_file}")
                else:
                    logger.warning(f"本地证书文件不存在: {local_file}")
            
            # 关闭连接
            sftp.close()
            ssh_client.close()
            
            if pushed_files:
                logger.info(f"成功推送 {len(pushed_files)} 个证书文件到 {ssh_host}:{remote_domain_path}")
                return {
                    "success": True,
                    "message": f"证书 {domain} 已成功推送到远程主机",
                    "domain": domain,
                    "remote_host": ssh_host,
                    "remote_path": remote_domain_path,
                    "pushed_files": pushed_files
                }
            else:
                return {
                    "success": False,
                    "message": f"没有找到域名 {domain} 的证书文件",
                    "domain": domain,
                    "local_path": local_domain_path
                }
                
        except Exception as e:
            logger.error(f"SSH推送证书失败: {str(e)}")
            return {
                "success": False,
                "message": f"SSH推送失败: {str(e)}",
                "domain": domain,
                "error": str(e)
            }
    


    def list_local_certificates(self) -> List[str]:
        """列出本地所有证书域名"""
        try:
            local_cert_path = self.config['local_cert_path']
            if not os.path.exists(local_cert_path):
                return []
            
            domains = [d for d in os.listdir(local_cert_path) 
                      if os.path.isdir(os.path.join(local_cert_path, d))]
            return domains
            
        except Exception as e:
            logger.error(f"列出本地证书失败: {str(e)}")
            return []


def push_certificate_to_remote(domain: str) -> Dict[str, Any]:
    """推送指定域名证书到远程主机的便捷函数"""
    pusher = SSLCertificatePusher()
    return pusher.push_certificate(domain)





if __name__ == "__main__":
    # 测试推送功能
    pusher = SSLCertificatePusher()

    # 列出本地证书
    domains = pusher.list_local_certificates()
    print(f"本地证书域名: {domains}")

    if domains:
        # 推送第一个域名的证书
        result = pusher.push_certificate(domains[0])
        print(f"推送结果: {result}")
    else:
        print("没有找到本地证书")
