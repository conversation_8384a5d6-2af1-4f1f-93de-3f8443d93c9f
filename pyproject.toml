[project]
name = "ops-agent"
version = "1.0.0"
description = "基础自动化运维框架"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "python-dotenv>=1.0.0",
    "langchain>=0.3.25",
    "langchain-core>=0.3.65",
    "langchain-openai>=0.3.23",
    "langgraph>=0.4.8",
    "pyyaml>=6.0.2",
    "requests>=2.32.4",
    "fastapi>=0.111.0",
    "uvicorn>=0.29.0",
    "pydantic>=2.0.0",
    "beautifulsoup4>=4.12.0",
    "schedule>=1.2.0",
    "aiosmtplib>=3.0.0",
]
