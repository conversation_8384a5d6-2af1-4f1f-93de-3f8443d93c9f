[project]
name = "ops-agent"
version = "1.0.0"
description = "企业级自动化运维框架，支持用户管理、邮箱管理、SSL证书管理等功能"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "azure-identity>=1.23.0",
    "dotenv>=0.9.9",
    "langchain>=0.3.25",
    "langchain-core>=0.3.65",
    "langchain-openai>=0.3.23",
    "langfuse>=2.60.2",
    "langgraph>=0.4.8",
    "markdownify>=1.1.0",
    "msal>=1.32.3",
    "msgraph-sdk>=1.33.0",
    "pyyaml>=6.0.2",
    # LetsEncrypt SSL证书管理依赖
    "acme>=2.11.0",
    "boto3>=1.35.0",
    "josepy>=1.14.0",
    "pyopenssl>=24.0.0",
    # Alibaba Cloud DNS SDK 依赖
    "alibabacloud-alidns20150109>=3.0.0",
    "alibabacloud_tea_openapi>=0.3.4",
    "certbot>=4.1.1",
    "certbot-dns-route53>=4.1.1",
    "certbot-dns-aliyun>=2.0.0",
    "certbot-dns-cloudflare>=4.1.1",
    "requests>=2.32.4",
    "mcp>=1.12.2",
    "fastapi>=0.111.0",
    "uvicorn>=0.29.0",
    "paramiko>=3.4.0",
]
