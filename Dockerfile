# 使用官方的 Python 3.12 slim 镜像作为基础
FROM registry.g.inhandcloud.com:5000/library/python:3.12-slim

# 设置工作目录
WORKDIR /app

ENV http_proxy=http://**********:8118
ENV https_proxy=http://**********:8118


RUN pip install --no-cache-dir uv


# 复制项目依赖定义文件
COPY pyproject.toml uv.lock /app/

# 使用 uv 同步依赖（创建虚拟环境）
RUN uv sync

# 复制整个项目代码到工作目录
COPY . /app

# 预安装 MCP 服务器依赖以避免运行时重复安装
RUN cd /app/mcp-server/zabbix-mcp-server && \
    uv sync && \
    echo "MCP 服务器依赖已预安装"

# 清除代理设置
ENV http_proxy= https_proxy=

# 暴露 FastAPI 应用运行的端口
EXPOSE 8000

# 设置容器启动时执行的命令
# 使用 uv run 确保在正确的虚拟环境中运行 uvicorn
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
