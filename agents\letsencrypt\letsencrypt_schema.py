# -*- coding: utf-8 -*-

"""
File: letsencrypt_schema.py
Author: AI Assistant
Date: 2025/6/19

LetsEncrypt 证书签发工具的数据模型定义。
"""

from typing import Dict, Optional, List, Any
from pydantic import BaseModel, Field, validator
import re


class IssueCertificateSchema(BaseModel):
    """签发证书参数模型"""
    domains: List[str] = Field(..., description="需要签发证书的域名列表，支持多个域名和通配符域名，例如: ['example.com', '*.example.com']")
    provider: str = Field(..., description="DNS提供商，支持: aws, aliyun, cloudflare, godaddy")
    email: Optional[str] = Field(None, description="Let's Encrypt 账户邮箱，用于接收通知")
    force_renewal: bool = Field(False, description="是否强制更新已存在的证书")
    key_type: str = Field("rsa", description="密钥类型: rsa, ecdsa")
    preferred_chain: str = Field("ISRG Root X1", description="首选证书链")
    cert_name: Optional[str] = Field(None, description="证书名称，用于更新现有证书或切换密钥类型")
    
    @validator('domains')
    def validate_domains(cls, v):
        """验证域名格式，支持通配符域名"""
        if not v:
            raise ValueError("域名列表不能为空")

        # 普通域名模式
        domain_pattern = re.compile(
            r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
        )

        # 通配符域名模式
        wildcard_pattern = re.compile(
            r'^\*\.(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
        )

        for domain in v:
            if not (domain_pattern.match(domain) or wildcard_pattern.match(domain)):
                raise ValueError(f"无效的域名格式: {domain}")

        return v
    
    @validator('provider')
    def validate_provider(cls, v):
        """验证DNS提供商"""
        supported_providers = ['aws', 'aliyun', 'cloudflare', 'godaddy']
        if v.lower() not in supported_providers:
            raise ValueError(f"不支持的DNS提供商: {v}，支持的提供商: {', '.join(supported_providers)}")
        return v.lower()


class RevokeCertificateSchema(BaseModel):
    """撤销证书参数模型"""
    domain: str = Field(..., description="要撤销证书的域名")
    reason: Optional[str] = Field(None, description="撤销原因")


class RenewCertificateSchema(BaseModel):
    """续期证书参数模型"""
    domain: Optional[str] = Field(None, description="要续期的特定域名，如果不指定则续期所有即将过期的证书")
    force: bool = Field(False, description="是否强制续期")


class ListCertificatesSchema(BaseModel):
    """列出证书参数模型"""
    domain_filter: Optional[str] = Field(None, description="域名过滤条件，支持通配符")
    show_expired: bool = Field(False, description="是否显示已过期的证书")


class GetCertificateInfoSchema(BaseModel):
    """获取证书信息参数模型"""
    domain: str = Field(..., description="要查询的域名")


class ExportCertificateSchema(BaseModel):
    """导出证书参数模型"""
    domain: str = Field(..., description="要导出证书的域名")
    export_format: str = Field("pem", description="导出格式: pem, p12, jks")
    output_path: Optional[str] = Field(None, description="导出路径，如果不指定则使用默认路径")
    
    @validator('export_format')
    def validate_export_format(cls, v):
        """验证导出格式"""
        supported_formats = ['pem', 'p12', 'jks']
        if v.lower() not in supported_formats:
            raise ValueError(f"不支持的导出格式: {v}，支持的格式: {', '.join(supported_formats)}")
        return v.lower()


class CheckCertificateStatusSchema(BaseModel):
    """检查证书状态参数模型"""
    domain: str = Field(..., description="要检查的域名")
    check_online: bool = Field(True, description="是否检查在线证书状态")


class ConfigureDNSSchema(BaseModel):
    """配置DNS提供商参数模型"""
    provider: str = Field(..., description="DNS提供商: aws, aliyun, cloudflare, godaddy")
    credentials: Dict[str, str] = Field(..., description="DNS提供商的认证信息")
    
    @validator('provider')
    def validate_provider(cls, v):
        """验证DNS提供商"""
        supported_providers = ['aws', 'aliyun', 'cloudflare', 'godaddy']
        if v.lower() not in supported_providers:
            raise ValueError(f"不支持的DNS提供商: {v}，支持的提供商: {', '.join(supported_providers)}")
        return v.lower()


class BatchIssueCertificateSchema(BaseModel):
    """批量签发证书参数模型"""
    certificate_configs: List[Dict[str, Any]] = Field(..., description="证书配置列表，每个配置包含域名和提供商信息")
    parallel: bool = Field(False, description="是否并行处理")
    max_workers: int = Field(3, description="最大并行工作线程数")
    
    @validator('max_workers')
    def validate_max_workers(cls, v):
        """验证最大工作线程数"""
        if v < 1 or v > 10:
            raise ValueError("最大工作线程数必须在1-10之间")
        return v
