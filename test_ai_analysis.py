# -*- coding: utf-8 -*-

"""
测试AI分析功能
"""

import asyncio
import os
from dotenv import load_dotenv

load_dotenv()

from agents.github_trending_agent import GitHubTrendingAgent


async def test_ai_analysis():
    """
    测试AI分析功能
    """
    # 检查是否配置了OpenAI API Key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ 请先配置 OPENAI_API_KEY 环境变量")
        return
    
    print("🚀 开始测试AI分析功能...")
    
    # 初始化代理
    agent = GitHubTrendingAgent()
    
    # 测试数据
    test_repo = {
        'name': 'microsoft/vscode',
        'description': 'Visual Studio Code',
        'language': 'TypeScript',
        'readme': '''# Visual Studio Code

Visual Studio Code is a lightweight but powerful source code editor which runs on your desktop and is available for Windows, macOS and Linux. It comes with built-in support for JavaScript, TypeScript and Node.js and has a rich ecosystem of extensions for other languages and runtimes (such as C++, C#, Java, Python, PHP, Go, .NET).

## Installation

Download and install VS Code from https://code.visualstudio.com/

## Getting Started

1. Open VS Code
2. Open a folder or file
3. Start coding!

## Features

- IntelliSense
- Debugging
- Built-in Git
- Extensions
'''
    }
    
    try:
        # 执行AI分析
        print(f"📊 分析项目: {test_repo['name']}")
        
        result = await agent.ai_analyze_project(
            repo_name=test_repo['name'],
            description=test_repo['description'],
            readme=test_repo['readme'],
            language=test_repo['language']
        )
        
        # 显示结果
        print("\n✅ AI分析结果:")
        print("=" * 50)
        print(f"📝 项目描述:")
        print(result['description'])
        print(f"\n🎯 使用场景:")
        for i, scenario in enumerate(result['use_scenarios'], 1):
            print(f"  {i}. {scenario}")
        print(f"\n📖 使用指南:")
        print(result['usage_guide'])
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == '__main__':
    asyncio.run(test_ai_analysis())
