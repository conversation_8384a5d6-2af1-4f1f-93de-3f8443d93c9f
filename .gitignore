# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/
Dockerfile
# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# LetsEncrypt sensitive data (exclude runtime data, keep source code)
/letsencrypt/
/certificates/
*.pem
*.key
*.crt
*.csr

# Temporary files
*.tmp
*.temp
.cache/

# Documentation and scripts (keep local, exclude from git)
docs/
scripts/

# Agents
agents/letsencrypt/platform-domains.md
agents/letsencrypt/API配置说明.md

