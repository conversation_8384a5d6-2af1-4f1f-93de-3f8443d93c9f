# 智能平台域名匹配功能说明

## 🎯 **功能概述**

智能平台域名匹配功能基于企业97个业务平台的完整域名映射表，通过AI自然语言理解能力，实现模糊平台名称到具体域名的智能匹配和SSL证书自动签发。

## 📊 **支持的平台分类**

### **主要业务平台（97个）**

| 平台分类 | 平台数量 | 主要域名示例 | DNS提供商 |
|----------|----------|--------------|-----------|
| **Nezha平台** | 7个 | `*.inhandcloud.com` | Route53/阿里云 |
| **研发支撑平台** | 18个 | `gitlab.inhand.design`, `jira.inhand.design` | Route53/阿里云 |
| **IoT平台** | 5个 | `iot.inhand.com.cn`, `iot.inhandnetworks.com` | 阿里云/Route53 |
| **贩卖机平台** | 9个 | `*.smartvm.cn`, `smartvending.io` | 阿里云/Route53 |
| **Poweris平台** | 9个 | `poweris.inhand.online` | 阿里云/Route53 |
| **白鹰能源平台** | 7个 | `xenergy.inhandcloud.cn` | 阿里云/Route53 |
| **官网相关** | 12个 | `resources.inhand.com` | Route53/Cloudflare |
| **运维支撑平台** | 6个 | `zabbix.inhand.online` | 阿里云 |
| **其他平台** | 24个 | 各类业务域名 | 多DNS提供商 |

## 🤖 **智能匹配规则**

### **1. 平台名称匹配**
- **精确匹配**：`研发支撑` → 研发支撑平台相关的所有域名
- **模糊匹配**：`研发` → 研发支撑平台
- **别名匹配**：`开发支撑` → 研发支撑平台

### **2. 服务名称匹配**
- **服务匹配**：`gitlab` → `gitlab.inhand.design`
- **工具匹配**：`jira` → `jira.inhand.design`
- **组件匹配**：`nexus` → `nexus.inhand.design`

### **3. 环境关键词匹配**
- **地区匹配**：`海外` → 海外环境域名
- **环境匹配**：`开发` → 开发环境域名
- **类型匹配**：`测试` → 测试环境域名

### **4. 业务线匹配**
- **产品匹配**：`nezha` → Nezha平台相关域名
- **行业匹配**：`贩卖机` → 贩卖机平台相关域名
- **客户匹配**：`白鹰` → 白鹰能源平台相关域名

## 💡 **使用示例**

### **模糊平台名称匹配**
```bash
# 平台分类匹配
"为研发支撑平台申请证书"     # 列出18个研发支撑相关平台供选择
"给nezha平台申请证书"        # 列出7个Nezha平台供选择
"为贩卖机平台申请证书"       # 列出9个贩卖机平台供选择

# 业务线匹配
"为IoT平台申请证书"          # 列出5个IoT平台供选择
"给poweris申请证书"         # 列出9个Poweris平台供选择
"为白鹰能源申请证书"         # 列出7个白鹰能源平台供选择
```

### **具体服务名称匹配**
```bash
# 开发工具匹配
"给gitlab申请证书"          # 自动匹配到gitlab.inhand.design
"为jira申请证书"            # 自动匹配到jira.inhand.design
"给nexus申请证书"           # 自动匹配到nexus.inhand.design

# 监控工具匹配
"为zabbix申请证书"          # 自动匹配到zabbix.inhand.online
"给grafana申请证书"         # 自动匹配到grafana.inhand.online
```

### **环境+平台匹配**
```bash
# 地区+平台
"为nezha海外申请证书"       # 匹配到*.inhandcloud.com
"给iscada国内申请证书"      # 匹配到iscada.inhandcloud.cn
"为poweris国内申请证书"     # 匹配到poweris.inhand.online

# 环境+服务
"为gitlab开发环境申请证书"   # 匹配到开发环境的gitlab域名
"给jira测试环境申请证书"     # 匹配到测试环境的jira域名
```

## 🔄 **交互流程**

### **单一匹配流程**
```
用户输入: "给gitlab申请证书"
    ↓
AI识别: gitlab → gitlab.inhand.design
    ↓
自动选择: AWS Route53 (基于域名后缀)
    ↓
执行签发: 直接调用证书签发工具
```

### **多重匹配流程**
```
用户输入: "为研发支撑平台申请证书"
    ↓
AI识别: 研发支撑 → 18个相关平台
    ↓
提供选择: 列出编号选择列表
    ↓
用户选择: 选择具体平台或服务
    ↓
执行签发: 调用对应的证书签发工具
```

## 🎯 **技术优势**

### **1. 基于AI的自然语言理解**
- **无需记忆**：不需要记住确切的域名
- **容错性强**：支持拼写错误和模糊描述
- **上下文理解**：理解业务场景和环境需求

### **2. 数据驱动的智能匹配**
- **完整覆盖**：基于97个企业平台的完整映射
- **实时更新**：平台文档更新即时生效
- **准确匹配**：AI理解能力确保高准确率

### **3. 简化的运维操作**
- **一句话搞定**：复杂的证书签发简化为一句自然语言
- **批量操作**：支持平台级别的批量证书管理
- **智能推荐**：根据平台特性自动选择最佳配置

## 📚 **平台文档维护**

### **文档位置**
- **映射文档**：`agents/letsencrypt/platform-domains.md`
- **提示词文档**：`agents/letsencrypt/letsencrypt_prompt.md`

### **更新方式**
1. **添加新平台**：在`platform-domains.md`中添加新的平台和域名信息
2. **更新域名**：修改现有平台的域名配置
3. **调整分类**：重新组织平台分类和关键词

### **生效机制**
- **即时生效**：文档更新后重启代理即可生效
- **无需编码**：纯文档驱动，无需修改代码
- **版本控制**：文档变更通过Git进行版本管理

## 🚀 **扩展能力**

### **支持的扩展**
- **新业务平台**：轻松添加新的业务平台和域名
- **多语言支持**：支持中英文混合的平台描述
- **别名系统**：为平台配置多个别名和关键词
- **环境标识**：支持开发、测试、生产环境的自动识别

### **未来规划**
- **批量操作**：支持"为所有研发支撑平台申请证书"
- **智能推荐**：基于使用频率推荐相关平台
- **依赖分析**：识别平台间的依赖关系
- **证书监控**：基于平台重要性设置监控策略

---

**通过智能平台域名匹配功能，运维人员只需要说出平台名称或服务名称，系统就能自动找到对应的域名并完成SSL证书签发，大大简化了企业级证书管理的复杂度。**
