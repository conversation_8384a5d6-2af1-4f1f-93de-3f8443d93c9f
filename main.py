# -*- coding: utf-8 -*-

"""
File: main.py
Author: AI Assistant
Date: 2025/8/5

FastAPI aplication for the OPS Agent.
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from ops_agent import OpsAgent, ResultModel
from utils.logging_config import logger

app = FastAPI(
    title="AI Ops Agent",
    description="一个基于AI的自动化运维代理服务",
    version="1.0.0",
)

# 在应用启动时初始化OpsAgent
ops_agent = OpsAgent()

class OpsTask(BaseModel):
    task: str

@app.post("/api/ops_agent", response_model=ResultModel)
async def run_ops_agent(ops_task: OpsTask):
    """
    接收运维任务并调用OpsAgent执行。
    """
    logger.info(f"接收到API请求，任务: {ops_task.task}")
    try:
        result = await ops_agent.invoke_agent(ops_task.task)
        return result
    except Exception as e:
        logger.error(f"处理API请求时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
def read_root():
    return {"message": "欢迎使用 AI Ops Agent API"}

if __name__ == "__main__":
    import uvicorn
    # 这部分主要用于本地开发和调试
    uvicorn.run(app, host="0.0.0.0", port=8000)
