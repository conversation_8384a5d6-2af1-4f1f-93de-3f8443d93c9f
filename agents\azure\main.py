# -*- coding: utf-8 -*-

"""
File: main.py
Author: HuangJun
Date: 2025/6/17
"""

import asyncio
from agents.azure.azure_entraid_agent import EntraIDAgent

async def main():
    agent = EntraIDAgent()
    print("欢迎使用 Azure EntraID 管理工具")
    print("请输入您的指令（例如：'张三离职'），输入 'exit' 退出程序")
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n请输入指令: ").strip()
            
            if user_input.lower() == 'exit':
                print("感谢使用，再见！")
                break
                
            if not user_input:
                continue
            
            # 执行指令
            result = await agent.invoke(user_input)
            
            # 显示执行结果
            if result.status == "success":
                print(f"\n执行成功！")
                print(f"执行结果: {result.result}")
            else:
                print(f"\n执行失败！")
                print(f"失败原因: {result.failed_reason}")
                
        except Exception as e:
            print(f"\n发生错误: {str(e)}")
            continue

if __name__ == "__main__":
    asyncio.run(main())