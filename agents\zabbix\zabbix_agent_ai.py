#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能Zabbix代理 - 基于MCP框架重构

使用LangChain + MCP的标准架构，支持ReAct模式的智能工具选择和规划。
集成本地zabbix-mcp-server，使用官方MCP SDK进行通信。
"""

import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

from langchain.agents import AgentExecutor, create_react_agent
from langchain_core.prompts import PromptTemplate
from langchain_core.tools import Tool

from utils.logging_config import logger
from llm.llm import get_langfuse_callbacks, llm
from utils.opsany import OpsAny
from .zabbix_mcp_client import ZabbixMCPClient

class ZabbixAgentAI:
    """
    智能Zabbix代理 - 支持单主机ReAct分析和主机群组综合报告生成。
    """

    def __init__(self):
        """初始化智能Zabbix代理"""
        self.llm = llm
        self.zabbix_tools = []
        self.agent_executor = None
        self.zabbix_client = ZabbixMCPClient()
        self.core_metrics = []
        self.react_prompt_template = ""

    async def _load_config_and_prompt(self):
        """从zabbix_prompt.md加载配置和提示词。"""
        if self.core_metrics and self.react_prompt_template:
            return

        try:
            prompt_file = Path(__file__).parent / "zabbix_prompt.md"
            if not prompt_file.exists():
                raise FileNotFoundError(f"提示词文件不存在: {prompt_file}")

            content = prompt_file.read_text(encoding='utf-8')

            # 解析METRICS_CONFIG
            config_match = re.search(r'\[METRICS_CONFIG\](.*?)\[/METRICS_CONFIG\]', content, re.DOTALL)
            if not config_match:
                raise ValueError("在提示词文件中找不到 [METRICS_CONFIG] 块")
            
            config_str = config_match.group(1).strip()
            self.core_metrics = json.loads(config_str)
            logger.info(f"成功从提示词文件加载 {len(self.core_metrics)} 个核心监控项。")

            # 移除配置块，保留剩余部分作为模板
            self.react_prompt_template = content.replace(config_match.group(0), "").strip()

        except Exception as e:
            logger.error(f"加载和解析提示词文件失败: {e}", exc_info=True)
            # 提供一个回退机制，以防文件损坏或格式错误
            self.core_metrics = []
            self.react_prompt_template = "" # 或者一个默认的模板字符串
            raise
        
    async def process_task(self, task: str) -> Dict[str, Any]:
        """
        处理用户任务，并根据任务类型智能路由。
        - 对于群组分析任务，使用高效的“单次通过”模式。
        - 对于单主机或探索性任务，使用传统的ReAct Agent模式。
        """
        try:
            logger.info(f"开始处理Zabbix任务: {task}")
            await self._load_config_and_prompt()

            # 步骤1: 任务类型识别
            if self._is_group_analysis_task(task):
                logger.info("检测到群组分析任务，切换到“单次通过综合分析”模式。")
                return await self._handle_group_analysis(task)
            else:
                logger.info("检测到单主机/探索性任务，使用ReAct Agent模式。")
                return await self._handle_single_analysis(task)

        except Exception as e:
            logger.error(f"处理任务失败: {e}", exc_info=True)
            return {
                "status": "failed",
                "error": str(e),
                "result": f"任务处理失败: {str(e)}"
            }

    def _is_group_analysis_task(self, task: str) -> bool:
        """通过关键词判断是否为群组分析任务。"""
        group_keywords = ["主机群组", "主机组", "群组", "所有主机", "主机集群", "服务器组", "平台"]
        if any(keyword in task for keyword in group_keywords):
            return True
        if "报告" in task and len(task) > 10:
             return True
        return False

    async def _handle_single_analysis(self, task: str) -> Dict[str, Any]:
        """处理单主机或探索性任务（传统ReAct模式）。"""
        await self._initialize_mcp_tools()
        self._create_react_agent()
        result = await self._execute_agent_task(task)
        
        message = result.get("output", "任务执行完成")
        if "intermediate_steps" in result:
            for step in result.get("intermediate_steps", []):
                if len(step) >= 2 and isinstance(step[1], str):
                    raw_data = step[1]
                    message = f"{message}\n\n原始数据:\n{raw_data}"
                    break
        
        return {
            "success": True,
            "message": message,
            "data": result
        }

    async def _handle_group_analysis(self, task: str) -> Dict[str, Any]:
        """处理群组分析任务 - 使用全新的单次通过分析模式。"""
        try:
            logger.info("🚀 启动单次通过综合分析模式")
            await self._initialize_mcp_tools()

            group_name, metrics, time_range_str = self._parse_group_task(task)
            metric_names = [m['name'] for m in metrics]
            logger.info(f"群组分析参数: 群组={group_name}, 监控项={metric_names}, 时间范围={time_range_str}")

            analysis_result = await self._perform_group_analysis(group_name, metrics, time_range_str)

            return {
                "status": "success",
                "result": analysis_result,
                "analysis_type": "single_pass_comprehensive"
            }
        except Exception as e:
            logger.error(f"群组分析失败: {e}", exc_info=True)
            return {
                "status": "failed",
                "error": str(e),
                "result": f"❌ 群组分析任务执行失败: {str(e)}"
            }

    def _parse_group_task(self, task: str) -> tuple:
        """从用户任务中解析出群组分析所需的参数。"""
        group_name = None  # 不设置默认值，强制从任务中解析
        time_range = "最近半年"

        # 更强的正则表达式来匹配群组名称，按优先级排序
        group_patterns = [
            # 1. 完整的 InConnect-xxx 格式
            r"(InConnect[a-zA-Z0-9\-]*(?:海外|国内|国外|境外|境内|overseas|domestic))",
            # 2. InConnect 开头的任何格式
            r"(InConnect[a-zA-Z0-9\-]*)",
            # 3. 包含地区标识的群组名称
            r"([a-zA-Z0-9\-]*(?:海外|国内|国外|境外|境内)(?:主机群组|主机组|群组|服务器组|平台)?)",
            # 4. 通用群组名称格式
            r"([a-zA-Z0-9\-]+(?:主机群组|主机组|群组|服务器组|平台))",
            # 5. 单独的地区标识（作为最后备选）
            r"(海外|国内|国外|境外|境内)(?:主机群组|主机组|群组|服务器组|平台)?"
        ]

        for pattern in group_patterns:
            group_match = re.search(pattern, task)
            if group_match:
                group_name_raw = group_match.group(1)
                # 清理群组名称，移除后缀关键词
                for keyword in ["主机群组", "主机组", "群组", "服务器组", "平台"]:
                    if keyword in group_name_raw:
                        group_name = group_name_raw.replace(keyword, "").strip()
                        break
                else:
                    group_name = group_name_raw.strip()

                # 如果只匹配到地区标识，转换为标准格式
                if group_name in ["海外", "国外", "境外"]:
                    group_name = "InConnect-海外"
                elif group_name in ["国内", "境内"]:
                    group_name = "InConnect-国内"

                break

        # 如果仍然没有找到群组名称，使用默认值
        if not group_name:
            group_name = "InConnect-国内"  # 作为最后的备选
        
        metrics = self.core_metrics

        if "三个月" in task: time_range = "最近三个月"
        elif "一个月" in task: time_range = "最近一个月"
        elif "一周" in task: time_range = "最近一周"
        
        return group_name, metrics, time_range

    async def _test_zabbix_connection(self) -> Dict[str, Any]:
        """测试 Zabbix 连接和认证状态"""
        try:
            # 尝试调用一个简单的 API 来测试连接
            result = await self.zabbix_client.call_tool("hostgroup_get", {})

            parsed_result = self._parse_mcp_response(result, "连接测试", raise_on_error=False)
            if parsed_result is None:
                return {"success": False, "error": "认证失败或权限不足"}

            return {"success": True, "message": "连接正常"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _parse_mcp_response(self, raw_response: Any, context: str, raise_on_error: bool = True) -> Optional[List[Dict]]:
        """统一的 MCP 响应解析器"""
        try:
            # 处理字符串响应
            if isinstance(raw_response, str):
                try:
                    response = json.loads(raw_response)
                except json.JSONDecodeError:
                    error_msg = f"解析{context}数据失败: {raw_response}"
                    if raise_on_error:
                        raise ValueError(error_msg)
                    logger.error(error_msg)
                    return None
            else:
                response = raw_response

            # 检查错误情况
            if isinstance(response, dict):
                if "error" in response:
                    error_msg = f"获取{context}失败: {response.get('error')}"
                    if raise_on_error:
                        raise ValueError(error_msg)
                    logger.error(error_msg)
                    return None
                elif "result" in response and "Error calling tool" in str(response.get("result", "")):
                    error_msg = f"MCP 工具调用失败: {response.get('result')}"
                    if raise_on_error:
                        raise ValueError(error_msg)
                    logger.error(error_msg)
                    return None

            # 确保返回列表
            if not isinstance(response, list):
                error_msg = f"{context}数据格式错误，期望列表但得到 {type(response)}: {response}"
                if raise_on_error:
                    raise ValueError(error_msg)
                logger.error(error_msg)
                return None

            return response

        except Exception as e:
            if raise_on_error:
                raise
            logger.error(f"解析{context}响应时发生异常: {e}")
            return None

    async def _perform_group_analysis(self, group_name: str, metrics: List[Dict[str, str]], time_range_str: str) -> str:
        """执行三阶段、单次通过的综合分析。"""
        logger.info("--- 阶段一: 集中式数据获取 ---")

        try:
            # 1. 首先测试连接和认证
            logger.info("🔍 测试 Zabbix MCP 连接和认证状态...")
            test_result = await self._test_zabbix_connection()
            if not test_result["success"]:
                return f"❌ Zabbix 连接测试失败: {test_result['error']}\n\n请检查以下配置：\n1. MCP 服务器是否正常运行\n2. Zabbix API Token 是否有效\n3. 用户权限是否足够"

            # 计算正确的时间范围
            # 计算时间范围
            now = datetime.now()
            time_till = int(now.timestamp())
            time_deltas = {
                "最近半年": 180, 
                "最近三个月": 90, 
                "最近一个月": 30, 
                "最近一周": 7
            }
            days_to_subtract = time_deltas.get(time_range_str, 30) # 默认30天
            time_from = int((now - timedelta(days=days_to_subtract)).timestamp())
            logger.info(f"时间范围: {datetime.fromtimestamp(time_from).strftime('%Y-%m-%d')} 到 {datetime.fromtimestamp(time_till).strftime('%Y-%m-%d')} (约 {days_to_subtract} 天)")

            # 2. 获取主机群组信息（增加详细日志）
            logger.info(f"📋 正在获取主机群组信息: {group_name}")
            group_info_raw = await self.zabbix_client.call_tool("hostgroup_get", {
                "filter": {"name": [group_name]}
            })

            # 3. 处理返回数据
            group_info = self._parse_mcp_response(group_info_raw, "主机群组")
            if not group_info:
                # 尝试模糊匹配
                logger.info(f"🔍 精确匹配失败，尝试模糊搜索包含 '{group_name}' 的群组...")
                all_groups_raw = await self.zabbix_client.call_tool("hostgroup_get", {})
                all_groups = self._parse_mcp_response(all_groups_raw, "所有主机群组")

                matching_groups = [g for g in all_groups if group_name.lower() in g.get('name', '').lower()]
                if matching_groups:
                    logger.info(f"找到 {len(matching_groups)} 个匹配的群组: {[g['name'] for g in matching_groups]}")
                    group_info = matching_groups  # 使用第一个匹配的群组
                else:
                    available_groups = [g.get('name', 'Unknown') for g in all_groups[:10]]  # 显示前10个
                    return f"❌ 找不到主机群组 '{group_name}'。\n\n可用的群组包括: {', '.join(available_groups)}"

            group_id = group_info[0]['groupid']
            logger.info(f"✅ 成功获取群组ID: {group_id}")

            # 4. 获取主机信息
            hosts_raw = await self.zabbix_client.call_tool("host_get", {"groupids": [group_id]})
            hosts = self._parse_mcp_response(hosts_raw, "主机")

            if not hosts:
                return f"❌ 主机群组 '{group_name}' 下没有任何主机。"

            host_map = {h['hostid']: h for h in hosts}
            host_ids = list(host_map.keys())
            logger.info(f"发现 {len(host_ids)} 台主机")

            # 5. 高效地获取监控项
            metric_keys = [m['key'] for m in metrics]
            logger.info(f"针对 {len(metric_keys)} 个核心指标键值进行精确搜索: {metric_keys}")

            # 使用 filter 参数在 Zabbix API 层进行过滤，这比获取所有项再在客户端过滤要高效得多
            items_raw = await self.zabbix_client.call_tool("item_get", {
                "hostids": host_ids,
                "filter": {"key_": metric_keys},
                "output": "extend"
            })
            items = self._parse_mcp_response(items_raw, "监控项")

            if not items:
                # 如果找不到，提供更有用的调试信息
                logger.warning(f"在主机 {host_ids} 上找不到指定的监控项键值: {metric_keys}。将获取一些示例键值用于调试。")
                example_items_raw = await self.zabbix_client.call_tool("item_get", {"hostids": host_ids, "limit": 10, "output": ["key_"]})
                example_items = self._parse_mcp_response(example_items_raw, "监控项示例", raise_on_error=False)
                example_keys = [item['key_'] for item in example_items] if example_items else ["未找到任何监控项"]
                return f"❌ 在指定主机上找不到任何与核心指标 {metric_keys} 匹配的监控项。\n\n找到的一些可用监控项键值示例: {example_keys}"

            # 6. 构建 item_map (现在 items 列表已经是精确匹配的结果)
            item_map = {item['itemid']: item for item in items}
            all_item_ids = list(item_map.keys())

            if not all_item_ids:
                return f"❌ 在指定主机上找不到任何与 '{[m['name'] for m in metrics]}' 相关的监控项"
            logger.info(f"找到 {len(all_item_ids)} 个完全匹配的监控项ID，将获取所有这些监控项的数据。")

            # 7. 分批高效获取日度聚合趋势数据，以应对大规模查询
            logger.info(f"查询时间范围长达 {days_to_subtract} 天，将分批调用 get_daily_trends 以避免网关超时。")
            BATCH_SIZE = 10  # 每批处理10个监控项，这是一个在实践中比较安全的值
            all_daily_trends = []
            all_item_ids = list(item_map.keys())

            num_batches = (len(all_item_ids) + BATCH_SIZE - 1) // BATCH_SIZE

            for i in range(0, len(all_item_ids), BATCH_SIZE):
                batch_ids = all_item_ids[i:i + BATCH_SIZE]
                batch_num = (i // BATCH_SIZE) + 1
                logger.info(f"  - 获取趋势数据批次 {batch_num}/{num_batches} (共 {len(batch_ids)} 个监控项)...")

                daily_trends_raw = await self.zabbix_client.call_tool("get_daily_trends", {
                    "itemids": batch_ids,
                    "time_from": time_from,
                    "time_till": time_till
                })

                # 为每个批次单独解析，如果一个批次失败，不会影响其他批次
                batch_trends = self._parse_mcp_response(daily_trends_raw, f"日度趋势数据批次 {batch_num}", raise_on_error=True)
                if batch_trends:
                    all_daily_trends.extend(batch_trends)
            
            daily_trends = all_daily_trends

            # 8. 构建结构化数据
            structured_data = [
                {
                    "host_name": host_map.get(item_map[trend['itemid']]['hostid'], {}).get('name', 'Unknown Host'),
                    "item_name": item_map[trend['itemid']].get('name', 'Unknown Item'),
                    **trend
                }
                for trend in daily_trends if trend['itemid'] in item_map
            ]

            logger.info("--- 阶段二: 全局分析与报告生成 ---")
            report_prompt = self._build_comprehensive_report_prompt(group_name, metrics, time_range_str, structured_data)

            response = await self.llm.ainvoke(report_prompt, config={"callbacks": get_langfuse_callbacks()})
            final_report = response.content

            logger.info("--- 阶段三: 保存产出物 ---")
            session_id = f"{group_name.replace(' ', '_')}_{now.strftime('%Y%m%d_%H%M%S')}"
            output_dir = Path("data") / "zabbix_analysis" / session_id
            output_dir.mkdir(parents=True, exist_ok=True)

            data_file = output_dir / "structured_daily_data.json"
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(structured_data, f, ensure_ascii=False, indent=2)
            logger.info(f"结构化数据已保存到: {data_file}")

            # 保存报告文件
            report_file = output_dir / "comprehensive_report.md"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(final_report)
            logger.info(f"最终报告已保存到: {report_file}")

            # 上传报告到 CMDB（不影响主任务成功状态）
            upload_success = self._upload_report_to_cmdb(group_name, final_report, now)
            upload_msg = "✅ 报告已成功上传到 CMDB" if upload_success else "⚠️ 报告上传到 CMDB 失败，但本地报告已生成"

            # 返回简洁的成功状态，而不是完整报告内容
            return {
                "status": "success",
                "message": f"✅ {group_name} 群组分析报告生成完毕！",
                "details": {
                    "report_directory": str(output_dir),
                    "report_file": str(output_dir / "comprehensive_report.md"),
                    "data_file": str(output_dir / "structured_daily_data.json"),
                    "cmdb_upload": upload_success,
                    "upload_message": upload_msg,
                    "analysis_duration": f"{(datetime.now() - now).total_seconds():.1f}秒",
                    "hosts_analyzed": len(host_map),
                    "metrics_analyzed": len(structured_data)
                }
            }

        except Exception as e:
            logger.error(f"群组分析执行失败: {e}", exc_info=True)
            return {
                "status": "error",
                "message": f"❌ {group_name} 群组分析失败",
                "error": str(e),
                "details": {
                    "error_type": type(e).__name__,
                    "timestamp": datetime.now().isoformat()
                }
            }

    def _upload_report_to_cmdb(self, platform_name: str, report_content: str, timestamp: datetime) -> bool:
        """
        将评估报告上传到 CMDB 的 PingTaiZiYuanPingGu 模型
        参考 test_opsany.py 的最佳实践

        Args:
            platform_name: 平台名称
            report_content: 报告内容（Markdown格式）
            timestamp: 生成时间

        Returns:
            bool: 上传成功返回True，失败返回False
        """
        try:
            # 初始化 OpsAny 客户端
            opsany = OpsAny()

            # 生成报告名称：平台名称 + 时间 + 评估报告
            time_str = timestamp.strftime("%Y%m%d_%H%M%S")
            report_name = f"{platform_name}_{time_str}_评估报告"

            # 格式化报告内容以适配 CMDB 显示（转换换行符为 HTML 格式）
            formatted_content = self._format_report_for_cmdb(report_content)

            # 构造数据（参考 test_opsany.py 的数据格式）
            data = {
                "PingTaiZiYuanPingGu_name": report_name,
                "PingTaiZiYuanPingGu_VISIBLE_NAME": report_name,
                "PingTaiZiYuanPingGu_PingTai": platform_name,
                "PingTaiZiYuanPingGu_PingGuBaoGao": formatted_content
            }

            # 数据格式验证
            required_fields = [
                "PingTaiZiYuanPingGu_name",
                "PingTaiZiYuanPingGu_VISIBLE_NAME",
                "PingTaiZiYuanPingGu_PingTai",
                "PingTaiZiYuanPingGu_PingGuBaoGao"
            ]

            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                logger.error(f"❌ 数据格式错误，缺少必需字段: {missing_fields}")
                return False

            logger.info(f"📤 正在上传评估报告到 CMDB: {report_name}")
            logger.info(f"📊 报告大小: {len(report_content)} 字符")

            # 调用 OpsAny 插入数据
            success = opsany.insert_model_record("PingTaiZiYuanPingGu", data)

            if success:
                logger.info(f"✅ 评估报告已成功上传到 CMDB: {report_name}")
                return True
            else:
                logger.warning(f"⚠️ 评估报告上传到 CMDB 失败（可能是网络问题）: {report_name}")
                logger.info("💡 提示: 本地报告文件已保存，可稍后手动上传")
                return False

        except Exception as e:
            logger.warning(f"⚠️ 上传评估报告到 CMDB 时发生异常: {e}")
            logger.info("💡 提示: 这不影响报告生成，本地文件已保存")
            return False

    def _format_report_for_cmdb(self, report_content: str) -> str:
        """
        格式化报告内容以适配 CMDB 显示
        将 Markdown 格式转换为 HTML 格式，确保换行符正确显示

        Args:
            report_content: 原始 Markdown 报告内容

        Returns:
            str: 格式化后的 HTML 内容
        """
        try:
            # 将 Markdown 换行符转换为 HTML 换行符
            formatted_content = report_content.replace('\n', '<br/>')

            # 用 <p> 标签包装整个内容（参考测试脚本的成功格式）
            formatted_content = f"<p>{formatted_content}</p>"

            logger.info(f"📝 报告内容已格式化为 HTML 格式")
            logger.info(f"📊 原始长度: {len(report_content)} 字符 → 格式化后: {len(formatted_content)} 字符")

            return formatted_content

        except Exception as e:
            logger.warning(f"⚠️ 格式化报告内容时发生异常: {e}")
            logger.info("💡 使用原始内容作为备选")
            return report_content

    def _format_report_for_cmdb(self, report_content: str) -> str:
        """
        格式化报告内容以适配 CMDB 显示
        将 Markdown 格式转换为 HTML 格式，确保换行符正确显示

        Args:
            report_content: 原始 Markdown 报告内容

        Returns:
            str: 格式化后的 HTML 内容
        """
        try:
            # 将 Markdown 换行符转换为 HTML 换行符
            formatted_content = report_content.replace('\n', '<br/>')

            # 处理连续的换行符（空行）
            formatted_content = formatted_content.replace('<br/><br/>', '<br/><br/>')

            # 可选：用 <p> 标签包装整个内容（参考 CMDB 中的格式）
            formatted_content = f"<p>{formatted_content}</p>"

            logger.info(f"📝 报告内容已格式化为 HTML 格式，长度: {len(formatted_content)} 字符")

            return formatted_content

        except Exception as e:
            logger.warning(f"⚠️ 格式化报告内容时发生异常: {e}")
            logger.info("💡 使用原始内容作为备选")
            return report_content

    def _build_comprehensive_report_prompt(self, group_name: str, metrics: List[Dict[str, str]], time_range_str: str, data: List[Dict]) -> str:
        data_json_str = json.dumps(data, indent=2, ensure_ascii=False)
        metric_names = [m['name'] for m in metrics]
        return f'''
# 指令：SRE专家综合评估报告

## 1. 你的角色
你是一位顶级的站点可靠性工程师（SRE），拥有超过10年的大规模分布式系统运维和性能分析经验。你擅长从海量的监控数据中发现问题、定位瓶颈、预测风险，并给出专业、可落地的优化建议。

## 2. 你的任务
根据下面提供的、已经过预处理的Zabbix日度聚合监控数据，为 `{group_name}` 主机群组生成一份完整、专业、深入的性能评估报告。

### 报告要求
- **时间范围**: {time_range_str}
- **核心监控项**: {', '.join(metric_names)}
- **输出格式**: 必须是结构清晰、格式优美的Markdown。

## 3. 必须遵循的报告结构
你必须严格按照以下结构和标题生成报告，不得遗漏任何部分：

---

# `{group_name}` 主机群组性能评估报告

## 1. 报告摘要 (Executive Summary)
*   **总体评估**: 一句话总结该群组在过去 `{time_range_str}` 的整体健康状况（例如：健康、良好、亚健康、存在风险）。
*   **关键发现**: 使用列表形式，列出2-3个最主要的发现。
*   **核心建议**: 使用列表形式，列出2-3个最重要、最紧急的优化建议。

## 2. 群组整体性能分析
*   对每个核心监控项进行全局分析。
*   计算并展示整个群组的**平均值**、**峰值**（95分位最大值）、**谷值**。
*   分析整体资源使用趋势。
*   使用Markdown表格进行清晰展示。

## 3. 关键主机深入分析 (Top Talkers)
*   识别并分析群组中资源消耗最高的2-3台主机。
*   对每台关键主机，详细分析其各项指标的特点和潜在问题。

## 4. 风险评估与容量预测
*   **潜在风险**: 明确指出当前存在的风险点。
*   **容量预测**: 基于现有数据趋势，对未来的资源需求进行简单预测。
*   **稳定性分析**: 基于标准差（`value_std`）评估各主机、各指标的稳定性。

## 5. 结论与容量规划 (Conclusion & Capacity Planning)
*   **业务增长预测**: 
    *   基于`db_device_online_num`的历史数据，采用趋势外推法，预测未来6个月的业务增长量和增长率。
    *   明确给出预测数值（例如：预计未来6个月设备在线数将从目前的10,000台增长到12,000台，增长率20%）。
*   **资源压力评估**:
    *   结合业务增长预测，分析CPU、内存、磁盘使用率的增长趋势。
    *   判断预测的业务增长是否会对现有资源造成压力。
*   **容量规划结论**:
    *   **必须明确回答**：现有资源能否支撑未来6个月的业务增长？（回答“是”或“否”）
    *   如果回答“否”，必须明确指出哪个资源项（CPU、内存、磁盘）最先达到瓶颈，并提供具体的、可操作的扩容建议（例如：【紧急】建议在未来3个月内将`/data`磁盘空间从1TB扩大到1.5TB）。
    *   如果回答“是”，也要说明当前资源依然健康，并建议在何时（如：3个月后）进行下一次评估。

---

## 6. 原始数据上下文
以下是用于分析的、完整的日度聚合监控数据（JSON格式）。请基于这些数据进行你的所有分析。

```json
{data_json_str}
```

## 7. 开始分析
现在，请开始撰写你的专业评估报告。
'''

    async def _initialize_mcp_tools(self) -> None:
        """初始化MCP工具集。"""
        if self.zabbix_tools: return
        try:
            logger.info("初始化MCP工具集")
            self.zabbix_tools = await self._get_zabbix_mcp_tools()
            logger.info(f"MCP工具初始化完成: {len(self.zabbix_tools)} Zabbix工具")
        except Exception as e:
            logger.warning(f"MCP工具初始化失败: {e}")
            self.zabbix_tools = []
    
    async def _get_zabbix_mcp_tools(self) -> List[Tool]:
        """获取Zabbix MCP工具列表。"""
        available_tools = await self.zabbix_client.get_available_tools()
        return [
            Tool(
                name=tool_info.get("name"),
                description=tool_info.get("description"),
                func=self._create_async_tool_func(tool_info.get("name")),
                coroutine=self._create_async_tool_func(tool_info.get("name"))
            ) for tool_info in available_tools
        ]

    def _create_async_tool_func(self, name: str):
        """为工具创建一个异步调用函数。"""
        async def async_tool_func(tool_input: str) -> str:
            try:
                params = json.loads(tool_input) if isinstance(tool_input, str) and tool_input.strip() else (tool_input or {})
                result = await self.zabbix_client.call_tool(name, params)
                return json.dumps(result, ensure_ascii=False, indent=2)
            except Exception as e:
                return f"工具调用失败: {str(e)}"
        return async_tool_func

    def _build_react_template(self) -> str:
        """构建ReAct格式的模板。"""
        now = datetime.now()
        prompt_with_time = self.react_prompt_template.replace("{{CURRENT_DATETIME}}", now.strftime("%Y-%m-%d %H:%M:%S"))
        prompt_with_time = prompt_with_time.replace("{{CURRENT_TIMESTAMP}}", str(int(now.timestamp())))
        prompt_with_time = prompt_with_time.replace("{{TIMEZONE}}", str(now.astimezone().tzinfo))
        
        return f'''
{prompt_with_time}

可用工具:
{{tools}}

工具名称: {{tool_names}}

使用以下ReAct格式进行推理和行动:

Question: {{input}}
Thought: {{agent_scratchpad}}
'''

    def _create_react_agent(self) -> None:
        """创建ReAct模式的LangChain Agent。"""
        if self.agent_executor: return
        try:
            react_prompt_str = self._build_react_template()
            react_prompt = PromptTemplate.from_template(react_prompt_str)

            agent = create_react_agent(llm=self.llm, tools=self.zabbix_tools, prompt=react_prompt)
            self.agent_executor = AgentExecutor(
                agent=agent,
                tools=self.zabbix_tools,
                verbose=True,
                handle_parsing_errors=True,
                max_iterations=15,
                max_execution_time=300
            )
            logger.info("ReAct Agent创建成功")
        except Exception as e:
            logger.error(f"创建ReAct Agent失败: {e}", exc_info=True)
            raise

    async def _execute_agent_task(self, task: str) -> Dict[str, Any]:
        """执行Agent任务。"""
        if not self.agent_executor:
            raise ValueError("Agent未初始化")
        try:
            callbacks = get_langfuse_callbacks()
            result = await self.agent_executor.ainvoke({"input": task}, config={"callbacks": callbacks})
            return {
                "input": task,
                "output": result.get("output", ""),
                "intermediate_steps": result.get("intermediate_steps", [])
            }
        except Exception as e:
            logger.error(f"执行Agent任务失败: {e}", exc_info=True)
            return {"input": task, "output": f"任务执行失败: {str(e)}", "error": str(e)}
