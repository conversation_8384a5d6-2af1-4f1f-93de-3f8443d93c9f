# coding:utf-8

"""
File: aliyun_dns_script.py
Author: AI Assistant
Date: 2025/6/19

阿里云DNS自动化脚本，基于ywdblog的certbot-letencrypt-wildcardcertificates-alydns-au项目
"""

import base64
import urllib
import hmac
import datetime
import random
import string
import json
import sys
import os

# Python版本兼容
import hashlib

if sys.version_info[0] < 3:
    from urllib import quote
    from urllib import urlencode
    pv = "python2"
else:
    from urllib.parse import quote
    from urllib.parse import urlencode
    from urllib import request
    pv = "python3"


class AliDns:
    def __init__(self, access_key_id, access_key_secret, domain_name):
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.domain_name = domain_name

    @staticmethod
    def getDomain(domain):
        """提取根域名和子域名"""
        domain_parts = domain.split('.')
        if len(domain_parts) > 2:
            # 双级顶级域名列表（需要取最后三部分作为根域名）
            double_tlds = {
                "com.cn", "net.cn", "org.cn", "gov.cn", "edu.cn",
                "co.jp", "com.tw", "co.uk", "com.au"
            }

            # 检查是否是双级TLD
            last_two = '.'.join(domain_parts[-2:])
            if last_two in double_tlds:
                # 双级TLD：取最后三部分作为根域名
                if len(domain_parts) >= 3:
                    rootdomain = '.'.join(domain_parts[-3:])
                    selfdomain = '.'.join(domain_parts[:-3]) if len(domain_parts) > 3 else ""
                else:
                    rootdomain = domain
                    selfdomain = ""
            else:
                # 单级TLD：取最后两部分作为根域名
                rootdomain = '.'.join(domain_parts[-2:])
                selfdomain = '.'.join(domain_parts[:-2]) if len(domain_parts) > 2 else ""

            print(f"域名解析: {domain} -> 根域名: {rootdomain}, 子域名: {selfdomain}")
            return (selfdomain, rootdomain)
        return ("", domain)

    @staticmethod
    def generate_random_str(length=14):
        """生成随机字符串"""
        str_list = [random.choice(string.digits) for i in range(length)]
        random_str = ''.join(str_list)
        return random_str

    @staticmethod
    def percent_encode(str):
        """URL编码"""
        res = quote(str.encode('utf-8'), '')
        res = res.replace('+', '%20')
        res = res.replace('*', '%2A')
        res = res.replace('%7E', '~')
        return res

    @staticmethod
    def utc_time():
        """获取UTC时间"""
        time = datetime.datetime.now(datetime.timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        return time

    @staticmethod
    def sign_string(url_param):
        """生成签名字符串"""
        percent_encode = AliDns.percent_encode
        sorted_url_param = sorted(url_param.items(), key=lambda x: x[0])
        can_string = ''
        for k, v in sorted_url_param:
            can_string += '&' + percent_encode(k) + '=' + percent_encode(v)
        string_to_sign = 'GET' + '&' + '%2F' + '&' + percent_encode(can_string[1:])
        return string_to_sign

    @staticmethod
    def access_url(url):
        """访问URL"""
        if pv == "python2":
            f = urllib.urlopen(url)
            result = f.read().decode('utf-8')
            return json.loads(result)
        else:
            req = request.Request(url)
            with request.urlopen(req) as f:
                result = f.read().decode('utf-8')
                return json.loads(result)

    def visit_url(self, action_param):
        """访问阿里云DNS API"""
        common_param = {
            'Format': 'json',
            'Version': '2015-01-09',
            'AccessKeyId': self.access_key_id,
            'SignatureMethod': 'HMAC-SHA1',
            'Timestamp': AliDns.utc_time(),
            'SignatureVersion': '1.0',
            'SignatureNonce': AliDns.generate_random_str(),
            'DomainName': self.domain_name,
        }

        url_param = dict(common_param, **action_param)
        string_to_sign = AliDns.sign_string(url_param)
        hash_bytes = self.access_key_secret + "&"

        if pv == "python2":
            h = hmac.new(hash_bytes, string_to_sign, digestmod=hashlib.sha1)
            signature = base64.encodestring(h.digest()).strip()
        else:
            h = hmac.new(hash_bytes.encode('utf-8'), string_to_sign.encode('utf-8'), digestmod='SHA1')
            signature = base64.encodebytes(h.digest()).strip()

        url_param.setdefault('Signature', signature)
        url = 'https://alidns.aliyuncs.com/?' + urlencode(url_param)

        print(f"调用阿里云DNS API: {action_param.get('Action', 'Unknown')} for domain: {self.domain_name}")
        try:
            result = AliDns.access_url(url)
            # 检查API返回的错误
            if isinstance(result, dict) and "Code" in result:
                print(f"阿里云DNS API返回错误: {result.get('Code')} - {result.get('Message', '未知错误')}")
                if result.get('Code') == 'DomainNotExists':
                    print(f"错误详情: 域名 {self.domain_name} 不存在于阿里云DNS中")
                    print("建议: 请确认域名已在阿里云DNS中添加并托管")
            return result
        except Exception as e:
            print(f"阿里云DNS API调用失败: {str(e)}")
            print(f"请求URL: {url}")
            if "404" in str(e):
                print(f"HTTP 404错误: 域名 {self.domain_name} 可能不存在于阿里云DNS中")
                print("建议: 请检查域名是否已在阿里云DNS控制台中添加")
            raise

    def check_domain_exists(self):
        """检查域名是否存在于阿里云DNS中"""
        try:
            action_param = dict(
                Action='DescribeDomains',
                PageNumber='1',
                PageSize='100',
            )
            result = self.visit_url(action_param)
            if isinstance(result, dict) and "Domains" in result:
                domains = result["Domains"]["Domain"]
                domain_names = [d["DomainName"] for d in domains]
                return self.domain_name in domain_names
            return False
        except Exception as e:
            print(f"检查域名存在性失败: {str(e)}")
            return False

    def describe_domain_records(self):
        """获取域名解析记录"""
        action_param = dict(
            Action='DescribeDomainRecords',
            PageNumber='1',
            PageSize='500',
        )
        result = self.visit_url(action_param)
        return result

    def add_domain_record(self, type, rr, value):
        """添加域名解析记录"""
        action_param = dict(
            Action='AddDomainRecord',
            RR=rr,
            Type=type,
            Value=value,
        )
        result = self.visit_url(action_param)
        return result

    def delete_domain_record(self, id):
        """删除域名解析记录"""
        action_param = dict(
            Action="DeleteDomainRecord",
            RecordId=id,
        )
        result = self.visit_url(action_param)
        return result


def main():
    """主函数"""
    if len(sys.argv) != 7:
        print("用法: python aliyun_dns_script.py <add/clean> <domain> <acme_challenge> <validation> <access_key> <secret_key>")
        sys.exit(1)
    
    print("阿里云DNS API调用开始")
    print("-".join(sys.argv))
    
    _, cmd, certbot_domain, acme_challenge, certbot_validation, ACCESS_KEY_ID, ACCESS_KEY_SECRET = sys.argv
    
    # 解析域名
    certbot_domain = AliDns.getDomain(certbot_domain)
    
    if certbot_domain[0] == "":
        selfdomain = acme_challenge
    else:
        selfdomain = acme_challenge + "." + certbot_domain[0]
    
    # 创建DNS客户端
    domain = AliDns(ACCESS_KEY_ID, ACCESS_KEY_SECRET, certbot_domain[1])

    # 验证域名是否存在于阿里云DNS中
    print(f"检查域名 {certbot_domain[1]} 是否存在于阿里云DNS中...")
    if not domain.check_domain_exists():
        print(f"错误: 域名 {certbot_domain[1]} 不存在于阿里云DNS中")
        print("请确认:")
        print("1. 域名已在阿里云DNS控制台中添加")
        print("2. AccessKey和SecretKey配置正确")
        print("3. AccessKey具有DNS管理权限")
        sys.exit(1)
    else:
        print(f"[OK] 域名 {certbot_domain[1]} 验证成功")

    if cmd == "add":
        # 添加TXT记录
        result = domain.add_domain_record("TXT", selfdomain, certbot_validation)
        if "Code" in result:
            print("阿里云DNS域名增加失败-" + str(result["Code"]) + ":" + str(result["Message"]))
            sys.exit(1)
        else:
            print(f"成功添加TXT记录: {selfdomain}.{certbot_domain[1]} = {certbot_validation}")
            
    elif cmd == "clean":
        # 删除TXT记录
        data = domain.describe_domain_records()
        if "Code" in data:
            print("阿里云DNS域名删除失败-" + str(data["Code"]) + ":" + str(data["Message"]))
            sys.exit(1)
        
        record_list = data["DomainRecords"]["Record"]
        if record_list:
            for item in record_list:
                if (item['RR'] == selfdomain and item['Type'] == 'TXT'):
                    result = domain.delete_domain_record(item['RecordId'])
                    if "Code" in result:
                        print("删除记录失败-" + str(result["Code"]) + ":" + str(result["Message"]))
                    else:
                        print(f"成功删除TXT记录: {selfdomain}.{certbot_domain[1]}")
    
    print("阿里云DNS API调用结束")


if __name__ == '__main__':
    main()
