# -*- coding: utf-8 -*-

"""
File: github_trending_scheduler.py
Author: AI Assistant
Date: 2025/8/27

GitHub趋势项目定时任务调度器
支持每日和每周定时获取GitHub热门项目并生成报告
"""

import schedule
import time
import asyncio
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.github_trending_agent import GitHubTrendingAgent
from utils.logging_config import logger


class GitHubTrendingScheduler:
    """
    GitHub趋势项目定时调度器
    """
    
    def __init__(self):
        """
        初始化调度器
        """
        github_token = os.getenv('GITHUB_TOKEN')
        self.agent = GitHubTrendingAgent(github_token)
        logger.info("GitHub趋势调度器初始化完成")
    
    async def run_daily_task(self):
        """
        执行每日趋势任务
        """
        try:
            logger.info("开始执行每日GitHub趋势任务")
            result = await self.agent.process_task("获取每日热门项目前10个")
            
            if result.get("status") == "success":
                logger.info(f"每日趋势任务执行成功: {result.get('message')}")
            else:
                logger.error(f"每日趋势任务执行失败: {result.get('message')}")
                
        except Exception as e:
            logger.error(f"每日趋势任务执行异常: {e}")
    
    async def run_weekly_task(self):
        """
        执行每周趋势任务
        """
        try:
            logger.info("开始执行每周GitHub趋势任务")
            result = await self.agent.process_task("获取每周热门项目前10个")
            
            if result.get("status") == "success":
                logger.info(f"每周趋势任务执行成功: {result.get('message')}")
            else:
                logger.error(f"每周趋势任务执行失败: {result.get('message')}")
                
        except Exception as e:
            logger.error(f"每周趋势任务执行异常: {e}")
    
    def daily_job(self):
        """
        每日任务包装器
        """
        asyncio.run(self.run_daily_task())
    
    def weekly_job(self):
        """
        每周任务包装器
        """
        asyncio.run(self.run_weekly_task())
    
    def start_scheduler(self):
        """
        启动定时调度器
        """
        # 配置定时任务
        # 每天上午9点执行每日趋势任务
        schedule.every().day.at("09:00").do(self.daily_job)
        
        # 每周一上午10点执行每周趋势任务
        schedule.every().monday.at("10:00").do(self.weekly_job)
        
        logger.info("定时任务已配置:")
        logger.info("- 每日趋势: 每天 09:00")
        logger.info("- 每周趋势: 每周一 10:00")
        
        print("GitHub趋势定时调度器已启动")
        print("定时任务配置:")
        print("- 每日趋势: 每天 09:00")
        print("- 每周趋势: 每周一 10:00")
        print("按 Ctrl+C 停止调度器")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logger.info("定时调度器已停止")
            print("\n定时调度器已停止")


def run_once(period='daily'):
    """
    立即执行一次任务（用于测试）
    
    Args:
        period: 'daily' 或 'weekly'
    """
    async def run_task():
        github_token = os.getenv('GITHUB_TOKEN')
        agent = GitHubTrendingAgent(github_token)
        
        task = f"获取{period}热门项目前10个"
        result = await agent.process_task(task)
        
        if result.get("status") == "success":
            print(f"任务执行成功!")
            print(result.get('message'))
        else:
            print(f"任务执行失败: {result.get('message')}")
    
    asyncio.run(run_task())


if __name__ == '__main__':
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'run-daily':
            print("立即执行每日趋势任务...")
            run_once('daily')
        elif command == 'run-weekly':
            print("立即执行每周趋势任务...")
            run_once('weekly')
        elif command == 'schedule':
            # 启动定时调度器
            scheduler = GitHubTrendingScheduler()
            scheduler.start_scheduler()
        else:
            print("用法:")
            print("  python github_trending_scheduler.py run-daily    # 立即执行每日任务")
            print("  python github_trending_scheduler.py run-weekly   # 立即执行每周任务")
            print("  python github_trending_scheduler.py schedule     # 启动定时调度器")
    else:
        print("GitHub趋势项目定时调度器")
        print()
        print("用法:")
        print("  python github_trending_scheduler.py run-daily    # 立即执行每日任务")
        print("  python github_trending_scheduler.py run-weekly   # 立即执行每周任务")
        print("  python github_trending_scheduler.py schedule     # 启动定时调度器")
        print()
        print("定时调度配置:")
        print("- 每日趋势: 每天 09:00")
        print("- 每周趋势: 每周一 10:00")
        print()
        print("环境变量配置 (可选):")
        print("- GITHUB_TOKEN: GitHub API token (提高API限制)")
