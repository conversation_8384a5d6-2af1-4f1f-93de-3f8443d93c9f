{"generated_at": "2025-09-01T16:16:23.576215", "period": "weekly", "total_projects": 10, "projects": [{"rank": 1, "name": "asgeirtj/system_prompts_leaks", "url": "https://github.com/asgeirtj/system_prompts_leaks", "language": "JavaScript", "total_stars": "17,640", "period_stars": "8,742", "simple_description": "Collection of extracted System Prompts from popular chatbots like ChatGPT, Claude & Gemini", "detailed_description": "](https://github.com/asgeirtj/system_prompts_leaks/blob/main/OpenAI/gpt-5-thinking.md)\nCollection of system message instructions for various publicly deployed chatbots.\nFeel free to do PR's.\nPlease use discussions tabs for discussions not the Issues tab.\nDiscord username: asgeirtj\nX profile: https://x.com/asgeirtj\n[![Star History Chart](https://api.star-history.com/svg?repos=asgeirtj/system_prompts_leaks&type=Date)](https://www.star-history.com/#asgeirtj/system_prompts_leaks&Date)", "use_scenarios": ["前端和Node.js应用开发"], "usage_guide": "请查看仓库README获取详细使用指南。", "original_description": "Collection of extracted System Prompts from popular chatbots like ChatGPT, Claude & Gemini"}, {"rank": 2, "name": "plait-board/drawnix", "url": "https://github.com/plait-board/drawnix", "language": "TypeScript", "total_stars": "10,203", "period_stars": "3,914", "simple_description": "<picture style=\"width: 320px\">\n<source media=\"(prefers-color-scheme: light)\" srcset=\"https://github.", "detailed_description": "<picture style=\"width: 320px\">\n<source media=\"(prefers-color-scheme: light)\" srcset=\"https://github.com/plait-board/drawnix/blob/develop/apps/web/public/logo/logo_drawnix_h.svg?raw=true\" />\n<source media=\"(prefers-color-scheme: dark)\" srcset=\"https://github.com/plait-board/drawnix/blob/develop/apps/web/public/logo/logo_drawnix_h_dark.svg?raw=true\" />\n<img src=\"https://github.com/plait-board/drawnix/blob/develop/apps/web/public/logo/logo_drawnix_h.svg?raw=true\" width=\"360\" alt=\"Drawnix logo and n...", "use_scenarios": ["前端和Node.js应用开发"], "usage_guide": "请查看仓库README获取详细使用指南。", "original_description": "开源白板工具（SaaS），一体化白板，包含思维导图、流程图、自由画等。All in one open-source whiteboard tool with mind, flowchart, freehand and etc."}, {"rank": 3, "name": "winapps-org/winapps", "url": "https://github.com/winapps-org/winapps", "language": "Shell", "total_stars": "8,308", "period_stars": "2,899", "simple_description": "<p align=\"center\"><img align=\"center\" width=\"700\" src=\".", "detailed_description": "<p align=\"center\"><img align=\"center\" width=\"700\" src=\"./icons/banner_light.svg#gh-light-mode-only\"/></p>\n<hr>\nRun Windows applications (including [Microsoft 365](https://www.microsoft365.com/) and [Adobe Creative Cloud](https://www.adobe.com/creativecloud.html)) on GNU/Linux with `KDE Plasma`, `GNOME` or `XFCE`, integrated seamlessly as if they were native to the OS.\n<p align=\"center\"><img src=\"./demo/demo.png\" width=1000 alt=\"WinApps Demonstration.\"></p>\nWinApps works by:\n1. Running Windows in...", "use_scenarios": ["通用软件开发和系统集成"], "usage_guide": "## Installation", "original_description": "Run Windows apps such as Microsoft Office/Adobe in Linux (Ubuntu/Fedora) and GNOME/KDE as if they were a part of the native OS, including Nautilus integration. Hard fork of https://github.com/Fmstrat/winapps/"}, {"rank": 4, "name": "HKUDS/DeepCode", "url": "https://github.com/HKUDS/DeepCode", "language": "Python", "total_stars": "4,737", "period_stars": "2,567", "simple_description": "\"DeepCode: Open Agentic Coding (Paper2Code & Text2Web & Text2Backend)\"", "detailed_description": "<table style=\"border: none; margin: 0 auto; padding: 0; border-collapse: collapse;\">\n<tr>\n<td align=\"center\" style=\"vertical-align: middle; padding: 10px; border: none; width: 250px;\">\n<img src=\"assets/logo.png\" alt=\"DeepCode Logo\" width=\"200\" style=\"margin: 0; padding: 0; display: block;\"/>\n</td>\n<td align=\"left\" style=\"vertical-align: middle; padding: 10px 0 10px 30px; border: none;\">\n<pre style=\"font-family: 'Courier New', monospace; font-size: 16px; color: #0EA5E9; margin: 0; padding: 0; tex...", "use_scenarios": ["Web应用开发", "数据科学、自动化脚本和后端开发"], "usage_guide": "请查看仓库README获取详细使用指南。", "original_description": "\"DeepCode: Open Agentic Coding (Paper2Code & Text2Web & Text2Backend)\""}, {"rank": 5, "name": "moeru-ai/airi", "url": "https://github.com/moeru-ai/airi", "language": "<PERSON><PERSON>", "total_stars": "12,118", "period_stars": "3,186", "simple_description": "<source\nwidth=\"100%\"\nsrcset=\".", "detailed_description": "<source\nwidth=\"100%\"\nsrcset=\"./docs/content/public/banner-dark-1280x640.avif\"\nmedia=\"(prefers-color-scheme: dark)\"\n/>\n<source\nwidth=\"100%\"\nsrcset=\"./docs/content/public/banner-light-1280x640.avif\"\nmedia=\"(prefers-color-scheme: light), (prefers-color-scheme: no-preference)\"\n/>\n<img width=\"250\" src=\"./docs/content/public/banner-light-1280x640.avif\" />\n</picture>\n<h1 align=\"center\">Project AIRI</h1>\n<p align=\"center\">Re-creating Neuro-sama, a soul container of AI waifu / virtual characters to bring...", "use_scenarios": ["AI/机器学习应用开发", "Web应用开发"], "usage_guide": "请查看仓库README获取详细使用指南。", "original_description": "💖🧸 Self hosted, you owned Grok Companion, a container of souls of waifu, cyber livings to bring them into our worlds, wishing to achieve Neuro-sama's altitude. Capable of realtime voice chat, Minecraft, Factorio playing. Web / macOS / Windows supported."}, {"rank": 6, "name": "HunxByts/GhostTrack", "url": "https://github.com/HunxByts/GhostTrack", "language": "Python", "total_stars": "5,265", "period_stars": "1,388", "simple_description": "Useful tool to track location or mobile number", "detailed_description": "Useful tool to track location or mobile number, so this tool can be called osint or also information gathering\n<img src=\"https://github.com/HunxByts/GhostTrack/blob/main/asset/bn.png\"/>\nNew update :\n```Version 2.2```\n```\nsudo apt-get install git\nsudo apt-get install python3\n```\n```\npkg install git\npkg install python3\n```\n```\ngit clone https://github.com/HunxByts/GhostTrack.git\ncd GhostTrack\npip3 install -r requirements.txt\npython3 GhostTR.py\n```\nDisplay on the menu ```IP Tracker```\n<img src=\"htt...", "use_scenarios": ["数据科学、自动化脚本和后端开发"], "usage_guide": "## Usage Tool\n```\ngit clone https://github.com/HunxByts/GhostTrack.git\ncd GhostTrack\npip3 install -r requirements.txt\npython3 GhostTR.py\n```\n\nDisplay on the menu ```IP Tracker```\n\n<img src=\"https://github.com/HunxByts/GhostTrack/blob/main/asset/ip.png \" />\n\non the IP Track menu, you can combo with the seeker tool to get the target IP\n<details>\n<summary>:zap: Install Seeker :</summary>\n- <strong><a href=\"https://github.com/thewhiteh4t/seeker\">Get Seeker</a></strong>\n</details>\n\nDisplay on the menu ```Phone Tracker```\n\n<img src=\"https://github.com/HunxByts/GhostTrack/blob/main/asset/phone.png\" />\n\non this menu you can search for information from the target phone number\n\nDisplay on the menu ```Username Tracker```\n\n<img src=\"https://github.com/HunxByts/GhostTrack/blob/main/asset/User.png\"/>\non this menu you can search for information from the target username on social media\n\n<details>\n<summary>:zap: Author :</summary>\n- <strong><a href=\"https://github.com/HunxByts\">HunxByts</a></strong>\n</", "original_description": "Useful tool to track location or mobile number"}, {"rank": 7, "name": "MODSetter/SurfSense", "url": "https://github.com/MODSetter/SurfSense", "language": "Python", "total_stars": "7,415", "period_stars": "884", "simple_description": "![new_header](https://github.", "detailed_description": "![new_header](https://github.com/user-attachments/assets/e236b764-0ddc-42ff-a1f1-8fbb3d2e0e65)\n<div align=\"center\">\n<a href=\"https://discord.gg/ejRNvftDp9\">\n<img src=\"https://img.shields.io/discord/1359368468260192417\" alt=\"Discord\">\n</a>\n</div>\nWhile tools like NotebookLM and Perplexity are impressive and highly effective for conducting research on any topic/query, SurfSense elevates this capability by integrating with your personal knowledge base. It is a highly customizable AI research agent,...", "use_scenarios": ["数据科学、自动化脚本和后端开发"], "usage_guide": "## Installation Options\n\nSurfSense provides two installation methods:\n\n1. **[Docker Installation](https://www.surfsense.net/docs/docker-installation)** - The easiest way to get SurfSense up and running with all dependencies containerized.\n   - Includes pgAdmin for database management through a web UI\n   - Supports environment variable customization via `.env` file\n   - Flexible deployment options (full stack or core services only)\n   - No need to manually edit configuration files between environments\n   - See [Docker Setup Guide](DOCKER_SETUP.md) for detailed instructions\n   - For deployment scenarios and options, see [Deployment Guide](DEPLOYMENT_GUIDE.md)\n\n2. **[Manual Installation (Recommended)](https://www.surfsense.net/docs/manual-installation)** - For users who prefer more control over their setup or need to customize their deployment.\n\nBoth installation guides include detailed OS-specific instructions for Windows, macOS, and Linux.\n\nBefore installation, make sure to complete the [prerequisite setup steps](https://www.surfsense.net/docs/) including:\n- PGVector setup\n- **File Processing ETL Service** (choose one):\n  - Unstructured.io API key (supports 34+ formats)\n  - LlamaIndex API key (enhanced parsing, supports 50+ formats)\n  - Docling (local processing, no API key required, supports PDF, Office docs, images, HTML, CSV)\n- Other required API keys", "original_description": "Open Source Alternative to NotebookLM / Perplexity, connected to external sources such as Search Engines, Slack, Linear, Jira, ClickUp, Confluence, Notion, YouTube, GitHub, Discord and more. Join our discord: https://discord.gg/ejRNvftDp9"}, {"rank": 8, "name": "Canner/WrenAI", "url": "https://github.com/Canner/WrenAI", "language": "TypeScript", "total_stars": "11,189", "period_stars": "1,183", "simple_description": "<p align=\"center\" id=\"top\">\n<a href=\"https://getwren.", "detailed_description": "<p align=\"center\" id=\"top\">\n<a href=\"https://getwren.ai/?utm_source=github&utm_medium=title&utm_campaign=readme\">\n<picture>\n<source media=\"(prefers-color-scheme: light)\" srcset=\"./misc/wrenai_logo.png\">\n<img src=\"./misc/wrenai_logo_white.png\" width=\"300px\">\n</picture>\n<h1 align=\"center\">Wren AI - Open-Source GenBI Agent</h1>\n</a>\n</p>\n<p align=\"center\">\n<a aria-label=\"Follow us on X\" href=\"https://x.com/getwrenai\">\n<img alt=\"\" src=\"https://img.shields.io/badge/-@getwrenai-blue?style=for-the-badg...", "use_scenarios": ["AI/机器学习应用开发", "数据存储和管理", "前端和Node.js应用开发"], "usage_guide": "请查看仓库README获取详细使用指南。", "original_description": "⚡️ GenBI (Generative BI) queries any database in natural language, generates accurate SQL (Text-to-SQL), charts (Text-to-Chart), and AI-powered insights in seconds."}, {"rank": 9, "name": "dockur/windows", "url": "https://github.com/dockur/windows", "language": "Shell", "total_stars": "39,794", "period_stars": "1,665", "simple_description": "Windows inside a Docker container.", "detailed_description": "<div align=\"center\">\n<a href=\"https://github.com/dockur/windows\"><img src=\"https://github.com/dockur/windows/raw/master/.github/logo.png\" title=\"Logo\" style=\"max-width:100%;\" width=\"128\" /></a>\n</div>\n<div align=\"center\">\n[![Build]][build_url]\n[![Version]][tag_url]\n[![Size]][tag_url]\n[![Package]][pkg_url]\n[![Pulls]][hub_url]\n</div></h1>\nWindows inside a Docker container.\n- ISO downloader\n- KVM acceleration\n- Web-based viewer\n[![Youtube](https://img.youtube.com/vi/xhGYobuG508/0.jpg)](https://www....", "use_scenarios": ["AI/机器学习应用开发"], "usage_guide": "## Usage 🐳", "original_description": "Windows inside a Docker container."}, {"rank": 10, "name": "twbs/bootstrap", "url": "https://github.com/twbs/bootstrap", "language": "MDX", "total_stars": "173,304", "period_stars": "457", "simple_description": "<a href=\"https://getbootstrap.", "detailed_description": "<a href=\"https://getbootstrap.com/\">\n<img src=\"https://getbootstrap.com/docs/5.3/assets/brand/bootstrap-logo-shadow.png\" alt=\"Bootstrap logo\" width=\"200\" height=\"165\">\n</a>\n</p>\n<h3 align=\"center\">Bootstrap</h3>\n<p align=\"center\">\nSleek, intuitive, and powerful front-end framework for faster and easier web development.\n<br>\n<a href=\"https://getbootstrap.com/docs/5.3/\"><strong>Explore Bootstrap docs »</strong></a>\n<br>\n<br>\n<a href=\"https://github.com/twbs/bootstrap/issues/new?assignees=-&labels=...", "use_scenarios": ["AI/机器学习应用开发", "Web应用开发"], "usage_guide": "请查看仓库README获取详细使用指南。", "original_description": "The most popular HTML, CSS, and JavaScript framework for developing responsive, mobile first projects on the web."}]}