# GitHub Weekly Trending Top 10

**生成时间**: 2025-09-01 16:16:23

**项目总数**: 10

---

## 1. asgeirtj/system_prompts_leaks

**编程语言**: JavaScript

**简单描述**: Collection of extracted System Prompts from popular chatbots like ChatGPT, Claude & Gemini

**详细描述**:
](https://github.com/asgeirtj/system_prompts_leaks/blob/main/OpenAI/gpt-5-thinking.md)
Collection of system message instructions for various publicly deployed chatbots.
Feel free to do PR's.
Please use discussions tabs for discussions not the Issues tab.
Discord username: asgeirtj
X profile: https://x.com/asgeirtj
[![Star History Chart](https://api.star-history.com/svg?repos=asgeirtj/system_prompts_leaks&type=Date)](https://www.star-history.com/#asgeirtj/system_prompts_leaks&Date)

**适用场景**:
- 前端和Node.js应用开发

**星标统计**: 总计 17,640 | weekly新增 8,742

**项目链接**: [asgeirtj/system_prompts_leaks](https://github.com/asgeirtj/system_prompts_leaks)

**使用指南**:
```
请查看仓库README获取详细使用指南。
```

---

## 2. plait-board/drawnix

**编程语言**: TypeScript

**简单描述**: <picture style="width: 320px">
<source media="(prefers-color-scheme: light)" srcset="https://github.

**详细描述**:
<picture style="width: 320px">
<source media="(prefers-color-scheme: light)" srcset="https://github.com/plait-board/drawnix/blob/develop/apps/web/public/logo/logo_drawnix_h.svg?raw=true" />
<source media="(prefers-color-scheme: dark)" srcset="https://github.com/plait-board/drawnix/blob/develop/apps/web/public/logo/logo_drawnix_h_dark.svg?raw=true" />
<img src="https://github.com/plait-board/drawnix/blob/develop/apps/web/public/logo/logo_drawnix_h.svg?raw=true" width="360" alt="Drawnix logo and n...

**适用场景**:
- 前端和Node.js应用开发

**星标统计**: 总计 10,203 | weekly新增 3,914

**项目链接**: [plait-board/drawnix](https://github.com/plait-board/drawnix)

**使用指南**:
```
请查看仓库README获取详细使用指南。
```

---

## 3. winapps-org/winapps

**编程语言**: Shell

**简单描述**: <p align="center"><img align="center" width="700" src=".

**详细描述**:
<p align="center"><img align="center" width="700" src="./icons/banner_light.svg#gh-light-mode-only"/></p>
<hr>
Run Windows applications (including [Microsoft 365](https://www.microsoft365.com/) and [Adobe Creative Cloud](https://www.adobe.com/creativecloud.html)) on GNU/Linux with `KDE Plasma`, `GNOME` or `XFCE`, integrated seamlessly as if they were native to the OS.
<p align="center"><img src="./demo/demo.png" width=1000 alt="WinApps Demonstration."></p>
WinApps works by:
1. Running Windows in...

**适用场景**:
- 通用软件开发和系统集成

**星标统计**: 总计 8,308 | weekly新增 2,899

**项目链接**: [winapps-org/winapps](https://github.com/winapps-org/winapps)

**使用指南**:
```
## Installation
```

---

## 4. HKUDS/DeepCode

**编程语言**: Python

**简单描述**: "DeepCode: Open Agentic Coding (Paper2Code & Text2Web & Text2Backend)"

**详细描述**:
<table style="border: none; margin: 0 auto; padding: 0; border-collapse: collapse;">
<tr>
<td align="center" style="vertical-align: middle; padding: 10px; border: none; width: 250px;">
<img src="assets/logo.png" alt="DeepCode Logo" width="200" style="margin: 0; padding: 0; display: block;"/>
</td>
<td align="left" style="vertical-align: middle; padding: 10px 0 10px 30px; border: none;">
<pre style="font-family: 'Courier New', monospace; font-size: 16px; color: #0EA5E9; margin: 0; padding: 0; tex...

**适用场景**:
- Web应用开发
- 数据科学、自动化脚本和后端开发

**星标统计**: 总计 4,737 | weekly新增 2,567

**项目链接**: [HKUDS/DeepCode](https://github.com/HKUDS/DeepCode)

**使用指南**:
```
请查看仓库README获取详细使用指南。
```

---

## 5. moeru-ai/airi

**编程语言**: Vue

**简单描述**: <source
width="100%"
srcset=".

**详细描述**:
<source
width="100%"
srcset="./docs/content/public/banner-dark-1280x640.avif"
media="(prefers-color-scheme: dark)"
/>
<source
width="100%"
srcset="./docs/content/public/banner-light-1280x640.avif"
media="(prefers-color-scheme: light), (prefers-color-scheme: no-preference)"
/>
<img width="250" src="./docs/content/public/banner-light-1280x640.avif" />
</picture>
<h1 align="center">Project AIRI</h1>
<p align="center">Re-creating Neuro-sama, a soul container of AI waifu / virtual characters to bring...

**适用场景**:
- AI/机器学习应用开发
- Web应用开发

**星标统计**: 总计 12,118 | weekly新增 3,186

**项目链接**: [moeru-ai/airi](https://github.com/moeru-ai/airi)

**使用指南**:
```
请查看仓库README获取详细使用指南。
```

---

## 6. HunxByts/GhostTrack

**编程语言**: Python

**简单描述**: Useful tool to track location or mobile number

**详细描述**:
Useful tool to track location or mobile number, so this tool can be called osint or also information gathering
<img src="https://github.com/HunxByts/GhostTrack/blob/main/asset/bn.png"/>
New update :
```Version 2.2```
```
sudo apt-get install git
sudo apt-get install python3
```
```
pkg install git
pkg install python3
```
```
git clone https://github.com/HunxByts/GhostTrack.git
cd GhostTrack
pip3 install -r requirements.txt
python3 GhostTR.py
```
Display on the menu ```IP Tracker```
<img src="htt...

**适用场景**:
- 数据科学、自动化脚本和后端开发

**星标统计**: 总计 5,265 | weekly新增 1,388

**项目链接**: [HunxByts/GhostTrack](https://github.com/HunxByts/GhostTrack)

**使用指南**:
```
## Usage Tool
```
git clone https://github.com/HunxByts/GhostTrack.git
cd GhostTrack
pip3 install -r requirements.txt
python3 GhostTR.py
```

Display on the menu ```IP Tracker```

<img src="https://github.com/HunxByts/GhostTrack/blob/main/asset/ip.png " />

on the IP Track menu, you can combo with the seeker tool to get the target IP
<details>
<summary>:zap: Install Seeker :</summary>
- <strong><a href="https://github.com/thewhiteh4t/seeker">Get Seeker</a></strong>
</details>

Display on the menu ```Phone Tracker```

<img src="https://github.com/HunxByts/GhostTrack/blob/main/asset/phone.png" />

on this menu you can search for information from the target phone number

Display on the menu ```Username Tracker```

<img src="https://github.com/HunxByts/GhostTrack/blob/main/asset/User.png"/>
on this menu you can search for information from the target username on social media

<details>
<summary>:zap: Author :</summary>
- <strong><a href="https://github.com/HunxByts">HunxByts</a></strong>
</
```

---

## 7. MODSetter/SurfSense

**编程语言**: Python

**简单描述**: ![new_header](https://github.

**详细描述**:
![new_header](https://github.com/user-attachments/assets/e236b764-0ddc-42ff-a1f1-8fbb3d2e0e65)
<div align="center">
<a href="https://discord.gg/ejRNvftDp9">
<img src="https://img.shields.io/discord/1359368468260192417" alt="Discord">
</a>
</div>
While tools like NotebookLM and Perplexity are impressive and highly effective for conducting research on any topic/query, SurfSense elevates this capability by integrating with your personal knowledge base. It is a highly customizable AI research agent,...

**适用场景**:
- 数据科学、自动化脚本和后端开发

**星标统计**: 总计 7,415 | weekly新增 884

**项目链接**: [MODSetter/SurfSense](https://github.com/MODSetter/SurfSense)

**使用指南**:
```
## Installation Options

SurfSense provides two installation methods:

1. **[Docker Installation](https://www.surfsense.net/docs/docker-installation)** - The easiest way to get SurfSense up and running with all dependencies containerized.
   - Includes pgAdmin for database management through a web UI
   - Supports environment variable customization via `.env` file
   - Flexible deployment options (full stack or core services only)
   - No need to manually edit configuration files between environments
   - See [Docker Setup Guide](DOCKER_SETUP.md) for detailed instructions
   - For deployment scenarios and options, see [Deployment Guide](DEPLOYMENT_GUIDE.md)

2. **[Manual Installation (Recommended)](https://www.surfsense.net/docs/manual-installation)** - For users who prefer more control over their setup or need to customize their deployment.

Both installation guides include detailed OS-specific instructions for Windows, macOS, and Linux.

Before installation, make sure to complete the [prerequisite setup steps](https://www.surfsense.net/docs/) including:
- PGVector setup
- **File Processing ETL Service** (choose one):
  - Unstructured.io API key (supports 34+ formats)
  - LlamaIndex API key (enhanced parsing, supports 50+ formats)
  - Docling (local processing, no API key required, supports PDF, Office docs, images, HTML, CSV)
- Other required API keys
```

---

## 8. Canner/WrenAI

**编程语言**: TypeScript

**简单描述**: <p align="center" id="top">
<a href="https://getwren.

**详细描述**:
<p align="center" id="top">
<a href="https://getwren.ai/?utm_source=github&utm_medium=title&utm_campaign=readme">
<picture>
<source media="(prefers-color-scheme: light)" srcset="./misc/wrenai_logo.png">
<img src="./misc/wrenai_logo_white.png" width="300px">
</picture>
<h1 align="center">Wren AI - Open-Source GenBI Agent</h1>
</a>
</p>
<p align="center">
<a aria-label="Follow us on X" href="https://x.com/getwrenai">
<img alt="" src="https://img.shields.io/badge/-@getwrenai-blue?style=for-the-badg...

**适用场景**:
- AI/机器学习应用开发
- 数据存储和管理
- 前端和Node.js应用开发

**星标统计**: 总计 11,189 | weekly新增 1,183

**项目链接**: [Canner/WrenAI](https://github.com/Canner/WrenAI)

**使用指南**:
```
请查看仓库README获取详细使用指南。
```

---

## 9. dockur/windows

**编程语言**: Shell

**简单描述**: Windows inside a Docker container.

**详细描述**:
<div align="center">
<a href="https://github.com/dockur/windows"><img src="https://github.com/dockur/windows/raw/master/.github/logo.png" title="Logo" style="max-width:100%;" width="128" /></a>
</div>
<div align="center">
[![Build]][build_url]
[![Version]][tag_url]
[![Size]][tag_url]
[![Package]][pkg_url]
[![Pulls]][hub_url]
</div></h1>
Windows inside a Docker container.
- ISO downloader
- KVM acceleration
- Web-based viewer
[![Youtube](https://img.youtube.com/vi/xhGYobuG508/0.jpg)](https://www....

**适用场景**:
- AI/机器学习应用开发

**星标统计**: 总计 39,794 | weekly新增 1,665

**项目链接**: [dockur/windows](https://github.com/dockur/windows)

**使用指南**:
```
## Usage 🐳
```

---

## 10. twbs/bootstrap

**编程语言**: MDX

**简单描述**: <a href="https://getbootstrap.

**详细描述**:
<a href="https://getbootstrap.com/">
<img src="https://getbootstrap.com/docs/5.3/assets/brand/bootstrap-logo-shadow.png" alt="Bootstrap logo" width="200" height="165">
</a>
</p>
<h3 align="center">Bootstrap</h3>
<p align="center">
Sleek, intuitive, and powerful front-end framework for faster and easier web development.
<br>
<a href="https://getbootstrap.com/docs/5.3/"><strong>Explore Bootstrap docs »</strong></a>
<br>
<br>
<a href="https://github.com/twbs/bootstrap/issues/new?assignees=-&labels=...

**适用场景**:
- AI/机器学习应用开发
- Web应用开发

**星标统计**: 总计 173,304 | weekly新增 457

**项目链接**: [twbs/bootstrap](https://github.com/twbs/bootstrap)

**使用指南**:
```
请查看仓库README获取详细使用指南。
```

---

