# -*- coding: utf-8 -*-

"""
File: video_publish_agent.py
Author: AI Assistant
Date: 2025/09/01

Video Publish Agent - 从结构化数据生成视频并发布到社交媒体平台。
"""

import asyncio
import os
from typing import Dict, List

import edge_tts
from moviepy import AudioFileClip, ColorClip, concatenate_videoclips

from utils.logging_config import logger


class VideoPublishAgent:
    """
    视频发布代理

    功能:
    - 根据分析报告生成视频脚本。
    - 使用TTS技术将脚本转换为语音。
    - 合成视频内容（图像、文字、音频）。
    - 自动发布视频到指定平台。
    """

    def __init__(self, voice: str = "zh-CN-YunxiNeural"):
        """
        初始化视频发布代理。
        """
        logger.info("视频发布代理初始化完成")
        self.output_dir = "videos"
        self.temp_dir = "temp_video_assets"
        self.voice = voice
        
        # 创建所需目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)


    async def create_video_from_report(self, report_data: Dict) -> str:
        """
        从报告数据完整地创建并返回视频文件路径。

        Args:
            report_data: 从 GitHubTrendingAgent 生成的结构化报告数据。

        Returns:
            生成的视频文件的路径。
        """
        logger.info("开始从报告创建视频...")
        
        # 1. 生成脚本和音频
        audio_segments = await self.generate_script_and_tts(report_data)
        
        if not audio_segments:
            logger.error("音频生成失败，无法创建视频。")
            return ""

        # 2. 创建视觉素材并合成视频
        video_path = await self.synthesize_video(report_data, audio_segments)
        
        # 3. (可选) 清理临时文件
        # await self.cleanup_temp_assets()

        logger.info(f"视频已成功生成: {video_path}")
        return video_path

    async def generate_script_and_tts(self, report_data: Dict) -> List[Dict]:
        """
        为报告中的每个项目生成脚本并转换为音频文件。

        Args:
            report_data: 报告数据。

        Returns:
            一个包含音频文件路径和时长的字典列表。
        """
        logger.info("正在生成视频脚本和音频...")
        audio_segments = []

        try:
            # 生成开场白音频
            title_script = f"您好，这是今天的 GitHub {report_data['period']} 趋势报告。我们为您精选了 {len(report_data['projects'])} 个热门项目。"
            title_audio_path = os.path.join(self.temp_dir, "audio_title.mp3")
            
            communicate = edge_tts.Communicate(title_script, self.voice)
            await communicate.save(title_audio_path)
            
            with AudioFileClip(title_audio_path) as audio_clip:
                duration = audio_clip.duration
            
            audio_segments.append({
                "script": title_script, 
                "audio_path": title_audio_path, 
                "duration": duration,
                "project_data": None  # 开场白没有项目数据
            })

            # 为每个项目生成音频
            for project in report_data['projects']:
                script = f'''
                第 {project['rank']} 名, {project['name']}.
                编程语言为 {project['language']}.
                它是一个 {project['description']}.
                '''""
                # 清理脚本以获得更好的TTS效果
                script = ' '.join(script.replace('\n', ' ').split())

                audio_path = os.path.join(self.temp_dir, f"audio_project_{project['rank']}.mp3")
                
                communicate = edge_tts.Communicate(script, self.voice)
                await communicate.save(audio_path)

                # 获取音频时长
                with AudioFileClip(audio_path) as audio_clip:
                    duration = audio_clip.duration

                audio_segments.append({
                    "script": script,
                    "audio_path": audio_path,
                    "duration": duration,
                    "project_data": project
                })
                logger.info(f"成功为项目 '{project['name']}' 生成音频。")

        except Exception as e:
            logger.error(f"生成音频文件时出错: {e}", exc_info=True)
            return [] # 如果出错则返回空列表

        logger.info("脚本和音频生成完毕。")
        return audio_segments

    async def synthesize_video(self, report_data: Dict, audio_segments: List[Dict]) -> str:
        """
        合成视频文件。

        Args:
            report_data: 报告数据，用于生成视觉元素。
            audio_segments: 包含音频路径和时长的列表。

        Returns:
            最终视频文件的路径。
        """
        logger.info("正在合成视频...")
        print("开始合成视频，包含以下音频片段：")
        for segment in audio_segments:
            print(f" - 音频: {segment['audio_path']}, 时长: {segment['duration']:.2f}s")

        try:
            # 确保输出目录存在
            os.makedirs(self.output_dir, exist_ok=True)

            # 创建视频片段列表
            video_clips = []

            for i, segment in enumerate(audio_segments):
                audio_path = segment['audio_path']
                duration = segment['duration']

                if not os.path.exists(audio_path):
                    logger.warning(f"音频文件不存在: {audio_path}")
                    continue

                # 加载音频
                audio_clip = AudioFileClip(audio_path)

                # 创建背景视频（纯色背景）
                background = ColorClip(size=(1920, 1080), color=(30, 30, 30), duration=duration)

                # 创建文本内容
                if i == 0:
                    # 标题片段
                    title_text = f"GitHub {report_data['period'].capitalize()} Trending"
                    subtitle_text = f"生成时间: {report_data['generated_at'][:10]}"

                    title_clip = TextClip(title_text,
                                        fontsize=80,
                                        color='white',
                                        font='Arial-Bold')
                    title_clip = title_clip.set_position('center').set_duration(duration)

                    subtitle_clip = TextClip(subtitle_text,
                                           fontsize=40,
                                           color='lightgray',
                                           font='Arial')
                    subtitle_clip = subtitle_clip.set_position(('center', 'bottom')).set_duration(duration)

                    # 合成视频片段
                    video_clip = CompositeVideoClip([background, title_clip, subtitle_clip])

                else:
                    # 项目介绍片段
                    project_index = i - 1
                    if project_index < len(report_data['projects']):
                        project = report_data['projects'][project_index]

                        # 项目标题
                        project_title = f"{project['rank']}. {project['name']}"
                        title_clip = TextClip(project_title,
                                            fontsize=60,
                                            color='white',
                                            font='Arial-Bold')
                        title_clip = title_clip.set_position(('center', 100)).set_duration(duration)

                        # 编程语言
                        lang_text = f"语言: {project['language']}"
                        lang_clip = TextClip(lang_text,
                                           fontsize=40,
                                           color='lightblue',
                                           font='Arial')
                        lang_clip = lang_clip.set_position(('center', 200)).set_duration(duration)

                        # 星标数
                        stars_text = f"⭐ {project['total_stars']} stars"
                        stars_clip = TextClip(stars_text,
                                            fontsize=40,
                                            color='yellow',
                                            font='Arial')
                        stars_clip = stars_clip.set_position(('center', 300)).set_duration(duration)

                        # 项目描述（截取前100字符）
                        desc_text = project['description'][:100] + "..." if len(project['description']) > 100 else project['description']
                        desc_clip = TextClip(desc_text,
                                           fontsize=30,
                                           color='lightgray',
                                           font='Arial',
                                           size=(1600, None),
                                           method='caption')
                        desc_clip = desc_clip.set_position(('center', 450)).set_duration(duration)

                        # 合成视频片段
                        video_clip = CompositeVideoClip([background, title_clip, lang_clip, stars_clip, desc_clip])
                    else:
                        # 如果没有对应的项目数据，创建简单的背景
                        video_clip = background

                # 设置音频
                video_clip = video_clip.set_audio(audio_clip)
                video_clips.append(video_clip)

            if not video_clips:
                logger.error("没有有效的视频片段可以合成")
                return None

            # 连接所有视频片段
            logger.info("正在连接视频片段...")
            final_video = concatenate_videoclips(video_clips)

            # 输出视频文件
            video_filename = os.path.join(self.output_dir, f"github_trending_{report_data['period']}.mp4")
            logger.info(f"正在写入视频文件: {video_filename}")

            # 写入视频文件
            final_video.write_videofile(
                video_filename,
                fps=24,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # 清理资源
            final_video.close()
            for clip in video_clips:
                clip.close()

            logger.info(f"视频合成完毕，路径: {video_filename}")
            return video_filename

        except Exception as e:
            logger.error(f"视频合成失败: {e}")
            return None

    async def publish_video(self, video_path: str, title: str, description: str, platform: str):
        """
        将视频发布到指定平台。

        Args:
            video_path: 要发布的视频文件路径。
            title: 视频标题。
            description: 视频描述。
            platform: 目标平台 (e.g., 'bilibili', 'douyin')。
        """
        logger.info(f"准备将视频 {video_path} 发布到 {platform}...")
        # TODO: 实现平台发布的自动化逻辑
        print(f"（模拟）正在发布到 {platform}...")
        await asyncio.sleep(1) # 模拟耗时操作
        logger.info(f"视频已成功发布到 {platform}。")

    async def cleanup_temp_assets(self):
        """
        清理生成视频过程中产生的临时文件。
        """
        logger.info("正在清理临时文件...")
        # TODO: 实现删除 self.temp_dir 中文件的逻辑
        print("（模拟）临时文件已清理。")
        await asyncio.sleep(0.5)
