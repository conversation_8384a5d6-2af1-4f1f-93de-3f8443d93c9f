# -*- coding: utf-8 -*-

"""
File: ops_agent.py
Author: AI Assistant & HuangJun
Date: 2025/6/20

OPS Agent - 统一运维代理
整合 Azure EntraID、腾讯企业邮箱、LetsEncrypt SSL证书管理等功能。
提供统一的自然语言接口进行运维操作。
"""

import os
from typing import Dict, Optional, List, Any
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from pydantic import BaseModel, Field
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent
from langchain.tools import tool


from agents.azure.azure_entraid_agent import EntraIDAgent
from agents.tencent.tencent_email_agent import TencentEmailAgent
from agents.letsencrypt.letsencrypt_agent_fixed import FixedLetsEncryptAgent as LetsEncryptAgent
# ZabbixAgentAI will be imported dynamically
from utils.logging_config import logger, log_tool_execution
from llm.llm import get_langfuse_callbacks,llm

class ResultModel(BaseModel):
    task: str = Field(..., description="任务执行内容")
    status: str = Field(..., description="操作结果状态，success | failed")
    result: str = Field(..., description="执行完任务后返回的结果，不更改工具返回的结果描述")
    failed_reason: Optional[str] = Field(None, description="执行失败时的原因，详细描述，如执行了什么操作，返回了什么结果")

class OpsAgent:
    """
    OPS Agent - 统一运维代理

    整合多个运维代理的功能，包括：
    - Azure EntraID 用户管理
    - 腾讯企业邮箱管理
    - LetsEncrypt SSL证书管理

    提供统一的自然语言接口来处理各种运维操作。
    """
    
    def __init__(self):
        """
        初始化 OPS Agent，使用懒加载方式初始化内部代理
        """
        self._entraid_agent = None
        self._email_agent = None
        self._letsencrypt_agent = None
        self._zabbix_agent = None
        logger.info("OPS Agent初始化完成")
    
    def _get_entraid_agent(self) -> EntraIDAgent:
        """
        懒加载方式获取 EntraID 代理
        """
        if self._entraid_agent is None:
            logger.info("初始化 EntraID 代理")
            self._entraid_agent = EntraIDAgent()
        return self._entraid_agent
    
    def _get_email_agent(self) -> TencentEmailAgent:
        """
        懒加载方式获取 Tencent Email 代理
        """
        if self._email_agent is None:
            logger.info("初始化 Tencent Email 代理")
            self._email_agent = TencentEmailAgent()
        return self._email_agent

    def _get_letsencrypt_agent(self) -> LetsEncryptAgent:
        """
        懒加载方式获取 LetsEncrypt 代理
        """
        if self._letsencrypt_agent is None:
            logger.info("初始化 LetsEncrypt 代理")
            self._letsencrypt_agent = LetsEncryptAgent()
        return self._letsencrypt_agent

    def _get_zabbix_agent(self):
        """
        懒加载方式获取 Zabbix 代理
        """
        if self._zabbix_agent is None:
            logger.info("初始化智能Zabbix代理")
            from agents.zabbix.zabbix_agent_ai import ZabbixAgentAI
            self._zabbix_agent = ZabbixAgentAI()
        return self._zabbix_agent

    async def call_entraid_tool(self):
        """
        调用 Azure EntraID 代理工具
        
        Returns:
            工具函数
        """
        class EntraIDToolSchema(BaseModel):
            task: str = Field(..., description="用户任务描述，包含对 Azure 的完整操作请求")

        @tool(args_schema=EntraIDToolSchema)
        @log_tool_execution(tool_name="call_entraid")
        async def call_entraid(task: str) -> Dict:
            """
            调用 Azure EntraID 代理处理用户账号相关操作
            
            Args:
                task: 用户任务描述
                
            Returns:
                操作结果
            """
            try:
                entraid_agent = self._get_entraid_agent()
                result = await entraid_agent.invoke(task)
                return result
            except Exception as e:
                logger.error(f"调用 EntraID 代理失败: {str(e)}", exc_info=True)
                return {
                    "task": task,
                    "status": "failed",
                    "result": "操作失败",
                    "failed_reason": f"调用 EntraID 代理失败: {str(e)}"
                }
        
        return call_entraid
    
    async def call_email_tool(self):
        """
        调用腾讯企业邮箱代理工具
        
        Returns:
            工具函数
        """
        class EmailToolSchema(BaseModel):
            task: str = Field(..., description="用户任务描述，包含对腾讯企业邮箱的操作请求")
        
        @tool(args_schema=EmailToolSchema)
        @log_tool_execution(tool_name="call_email")
        async def call_email(task: str) -> Dict:
            """
            调用腾讯企业邮箱代理处理邮箱相关操作
            
            Args:
                task: 用户任务描述
                
            Returns:
                操作结果
            """
            try:
                email_agent = self._get_email_agent()
                result = await email_agent.invoke(task)
                return result
            except Exception as e:
                logger.error(f"调用邮箱代理失败: {str(e)}", exc_info=True)
                return {
                    "task": task,
                    "status": "failed",
                    "result": "操作失败",
                    "failed_reason": f"调用邮箱代理失败: {str(e)}"
                }
        
        return call_email

    async def call_letsencrypt_tool(self):
        """
        调用 LetsEncrypt 代理工具

        Returns:
            工具函数
        """
        class LetsEncryptToolSchema(BaseModel):
            task: str = Field(..., description="用户任务描述，包含对SSL证书管理的操作请求")

        @tool(args_schema=LetsEncryptToolSchema)
        @log_tool_execution(tool_name="call_letsencrypt")
        async def call_letsencrypt(task: str) -> Dict:
            """
            调用 LetsEncrypt 代理处理SSL证书相关操作

            Args:
                task: 用户任务描述

            Returns:                操作结果
            """
            try:
                letsencrypt_agent = self._get_letsencrypt_agent()
                result = await letsencrypt_agent.invoke(task)
                return result
            except Exception as e:
                logger.error(f"调用 LetsEncrypt 代理失败: {str(e)}", exc_info=True)
                return {
                    "task": task,
                    "status": "failed",
                    "result": "操作失败",
                    "failed_reason": f"调用 LetsEncrypt 代理失败: {str(e)}"
                }

        return call_letsencrypt

    async def call_zabbix_tool(self):
        """
        调用 Zabbix 代理工具

        Returns:
            工具函数
        """
        class ZabbixToolSchema(BaseModel):
            task: str = Field(..., description="用户任务描述，包含对Zabbix监控分析的操作请求")

        @tool(args_schema=ZabbixToolSchema)
        @log_tool_execution(tool_name="call_zabbix")
        async def call_zabbix(task: str) -> Dict:
            """
            调用 Zabbix 代理处理所有Zabbix监控相关操作，具备强大的MCP集成能力

            🎯 核心能力：
            - 智能分析用户任务，自动选择合适的MCP方法
            - 支持复杂的多步骤监控数据分析
            - 生成专业的监控分析报告

            📊 支持的操作类型：
            1. 主机组管理：
               - 列出所有主机组
               - 获取指定主机组信息
               - 分析主机组性能状况

            2. 主机监控：
               - 列出所有主机或指定主机组下的主机
               - 查看单个主机的监控数据
               - 批量分析多台主机性能

            3. 监控数据分析：
               - CPU负载分析（支持历史数据）
               - 内存使用率分析
               - 磁盘空间和I/O分析
               - 网络流量分析
               - 多维度综合性能分析

            4. 问题诊断：
               - 获取当前告警和问题
               - 分析问题趋势
               - 生成问题诊断报告

            5. 报告生成：
               - 生成详细的性能分析报告
               - 提供具体的监控数据
               - 包含优化建议和风险评估

            🔧 技术特点：
            - 基于MCP (Model Context Protocol) 架构
            - 自动调用hostgroup_get、host_get、item_get、history_get等MCP方法
            - 支持时间范围查询（如最近3天、一周等）
            - 智能数据聚合和分析

            Args:
                task: 用户任务描述，支持复杂的监控分析需求

            Returns:
                包含详细监控数据和分析结果的操作结果
            """
            try:
                zabbix_agent = self._get_zabbix_agent()
                result = await zabbix_agent.process_task(task)

                # 处理新的返回格式（字典格式）和旧的返回格式（字符串格式）
                if isinstance(result, dict):
                    if result.get("status") == "success":
                        return {
                            "task": task,
                            "status": "success",
                            "result": result.get("message", "任务执行成功")
                        }
                    else:
                        return {
                            "task": task,
                            "status": "failed",
                            "result": result.get("message", "任务执行失败"),
                            "failed_reason": result.get("error", result.get("message", "未知错误"))
                        }
                elif isinstance(result, str):
                    # 兼容旧的字符串返回格式
                    if "✅" in result or "成功" in result:
                        return {
                            "task": task,
                            "status": "success",
                            "result": "任务执行成功"
                        }
                    else:
                        return {
                            "task": task,
                            "status": "failed",
                            "result": "任务执行失败",
                            "failed_reason": result
                        }
                else:
                    # 处理其他格式（如旧的 success 字段格式）
                    if result.get("success"):
                        return {
                            "task": task,
                            "status": "success",
                            "result": result.get("message", "任务执行成功")
                        }
                    else:
                        return {
                            "task": task,
                            "status": "failed",
                            "result": result.get("message", "任务执行失败"),
                            "failed_reason": result.get("message", "未知错误")
                        }
            except Exception as e:
                logger.error(f"调用 Zabbix 代理失败: {str(e)}", exc_info=True)
                return {
                    "task": task,
                    "status": "failed",
                    "result": "操作失败",
                    "failed_reason": f"调用 Zabbix 代理失败: {str(e)}"
                }

        return call_zabbix

    @log_tool_execution(tool_name="get_tools")
    async def get_tools(self) -> List:
        """
        获取所有可用工具的列表
        
        Returns:
            工具列表
        """
        tools = [
            await self.call_entraid_tool(),
            await self.call_email_tool(),
            await self.call_letsencrypt_tool(),
            await self.call_zabbix_tool(),
        ]
        logger.info(f"已加载工具列表: {[tool.name for tool in tools]}")
        return tools
    
    @log_tool_execution(tool_name="invoke_agent")
    async def invoke_agent(self, task: str) -> ResultModel:
        """
        使用 LangChain 的 agent_executor 模式调用工具
        
        Args:
            task: 用户任务描述
        
        Returns:
            agent_executor 的执行结果
            
        Raises:
            Exception: 调用失败
        """
        logger.info(f"开始处理任务: {task}")
        tools = await self.get_tools()
        
        prompt_file_path = os.path.join(os.path.dirname(__file__), 'ops_prompt.md')
        with open(prompt_file_path, 'r', encoding='utf-8') as f:
            system_prompt = f.read().strip()
        
        # 创建 agent_executor
        logger.info("创建 agent_executor")
        agent_executor = create_react_agent(
            llm.bind_tools(tools, parallel_tool_calls=False),
            tools=tools,
            response_format=ResultModel
        )
        
        # 执行 agent
        logger.info("开始执行 agent")
        try:
            # 获取Langfuse回调处理器
            callbacks = get_langfuse_callbacks()

            response = await agent_executor.ainvoke(
                input={
                    "messages": [
                        SystemMessage(system_prompt),
                        HumanMessage(task),
                    ]
                },
                config=RunnableConfig(
                    recursion_limit=100,  # 增加递归限制，允许更复杂的任务执行
                    callbacks=callbacks,  # 添加Langfuse追踪
                ),
            )
            
            structured_response = response.get("structured_response")
            logger.info(f"任务执行完成，状态: {structured_response.status}")
            # 不直接打印对象，避免输出干扰用户界面
            return structured_response
        except Exception as e:
            logger.error(f"执行agent时发生错误: {str(e)}", exc_info=True)
            # 创建一个错误响应
            error_response = ResultModel(
                task=task,
                status="failed",
                result="任务执行失败",
                failed_reason=f"执行过程中发生错误: {str(e)}"
            )
            return error_response


# 为了向后兼容，保留旧的类名别名
APIAgent = OpsAgent


async def main_cli():
    """
    命令行交互主函数
    """
    ops = OpsAgent()
    print("欢迎使用OPS Agent，请输入您的操作，输入'exit'结束程序")
    print("示例一： 张三入职，研发部，邮箱**********************,手机号15882201868")
    print("示例二： <EMAIL>.cn离职")
    print("示例三： 为example.com申请证书，使用AWS Route53")
    print("示例四： 检查www.baidu.com的证书状态")
    print("示例五： 对DM-DN4-国内主机群组最近半年的数据进行资源评估")

    while True:
        user_input = input("\n请输入任务描述: ")
        if user_input.lower() in ['退出', 'exit', 'quit']:
            print("程序已退出")
            break

        if not user_input.strip():
            print("任务描述不能为空，请重新输入")
            continue

        try:
            # 设置超时时间为180秒，防止任务执行时间过长
            async def invoke_with_timeout():
                return await asyncio.wait_for(ops.invoke_agent(user_input), timeout=180.0)
            
            try:
                result = await invoke_with_timeout()
                # 格式化输出结果，使其更易读
                print("\n===== 任务执行结果 =====")
                print(f"任务: {result.task}")
                print(f"状态: {result.status}")
                print(f"结果: {result.result}")
                if result.failed_reason:
                    print(f"失败原因: {result.failed_reason}")
                print("========================\n")
            except asyncio.TimeoutError:
                print("\n任务执行超时，可能是由于处理复杂性或系统资源限制导致。请尝试简化任务或稍后再试。\n")
        except Exception as e:
            print(f"\n任务执行出错: {str(e)}\n")
        finally:
            # Langfuse数据会自动刷新
            pass

if __name__ == '__main__':
    import asyncio
    # # 交互式模式（默认）
    asyncio.run(main_cli())

    # 单任务测试模式（开发调试用）
    # ops = OpsAgent()
    # asyncio.run(ops.invoke_agent("为gra-ggg.inhand.online申请证书，使用阿里云解析，密钥类型RSA"))
