# -*- coding: utf-8 -*-

"""
File: ops_agent.py
Author: AI Assistant
Date: 2025/8/27

OPS Agent - 基础运维代理框架
提供统一的自然语言接口进行运维操作的基础框架。
"""

import os
from typing import Dict, Optional, List, Any
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from pydantic import BaseModel, Field
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent
from langchain.tools import tool

from utils.logging_config import logger, log_tool_execution
from llm.llm import get_langfuse_callbacks, llm
from agents.github_trending_agent import GitHubTrendingAgent

class ResultModel(BaseModel):
    task: str = Field(..., description="任务执行内容")
    status: str = Field(..., description="操作结果状态，success | failed")
    result: str = Field(..., description="执行完任务后返回的结果，不更改工具返回的结果描述")
    failed_reason: Optional[str] = Field(None, description="执行失败时的原因，详细描述，如执行了什么操作，返回了什么结果")

class OpsAgent:
    """
    OPS Agent - 基础运维代理框架

    提供统一的自然语言接口来处理运维操作的基础框架。
    可以在此基础上扩展各种运维代理功能。
    """
    
    def __init__(self):
        """
        初始化 OPS Agent 基础框架
        """
        self._github_trending_agent = None
        logger.info("OPS Agent基础框架初始化完成")

    def _get_github_trending_agent(self) -> GitHubTrendingAgent:
        """
        懒加载方式获取 GitHub 趋势代理
        """
        if self._github_trending_agent is None:
            logger.info("初始化 GitHub 趋势代理")
            github_token = os.getenv('GITHUB_TOKEN')
            self._github_trending_agent = GitHubTrendingAgent(github_token)
        return self._github_trending_agent

    async def example_tool(self):
        """
        示例工具 - 展示如何创建新的运维工具
        
        Returns:
            工具函数
        """
        class ExampleToolSchema(BaseModel):
            task: str = Field(..., description="用户任务描述")

        @tool(args_schema=ExampleToolSchema)
        @log_tool_execution(tool_name="example_tool")
        async def example_operation(task: str) -> Dict:
            """
            示例运维操作工具
            
            Args:
                task: 用户任务描述
                
            Returns:
                操作结果
            """
            try:
                # 这里可以添加具体的运维操作逻辑
                logger.info(f"执行示例操作: {task}")
                
                return {
                    "task": task,
                    "status": "success",
                    "result": f"示例操作执行成功: {task}"
                }
            except Exception as e:
                logger.error(f"示例操作失败: {str(e)}", exc_info=True)
                return {
                    "task": task,
                    "status": "failed",
                    "result": "操作失败",
                    "failed_reason": f"示例操作失败: {str(e)}"
                }
        
        return example_operation

    async def github_trending_tool(self):
        """
        GitHub趋势项目分析工具

        Returns:
            工具函数
        """
        class GitHubTrendingToolSchema(BaseModel):
            task: str = Field(..., description="GitHub趋势分析任务描述，如'获取每日热门项目'、'分析本周趋势项目'等")

        @tool(args_schema=GitHubTrendingToolSchema)
        @log_tool_execution(tool_name="github_trending")
        async def github_trending_analysis(task: str) -> Dict:
            """
            GitHub趋势项目分析工具

            功能：
            - 获取GitHub每日/每周热门趋势项目
            - 分析项目详细信息和适用场景
            - 提取项目使用指南
            - 生成结构化报告（Markdown和JSON格式）

            支持的任务类型：
            - "获取每日热门项目" - 获取今日GitHub热门项目
            - "分析本周趋势项目" - 获取本周GitHub热门项目
            - "获取前10个热门项目" - 获取指定数量的热门项目

            Args:
                task: 任务描述，支持指定时间周期（每日/每周）和项目数量

            Returns:
                包含项目分析结果和报告文件路径的操作结果
            """
            try:
                github_agent = self._get_github_trending_agent()
                result = await github_agent.process_task(task)

                if result.get("status") == "success":
                    return {
                        "task": task,
                        "status": "success",
                        "result": result.get("message", "GitHub趋势分析完成")
                    }
                else:
                    return {
                        "task": task,
                        "status": "failed",
                        "result": "GitHub趋势分析失败",
                        "failed_reason": result.get("message", "未知错误")
                    }
            except Exception as e:
                logger.error(f"GitHub趋势分析失败: {str(e)}", exc_info=True)
                return {
                    "task": task,
                    "status": "failed",
                    "result": "操作失败",
                    "failed_reason": f"GitHub趋势分析失败: {str(e)}"
                }

        return github_trending_analysis

    @log_tool_execution(tool_name="get_tools")
    async def get_tools(self) -> List:
        """
        获取所有可用工具的列表
        
        Returns:
            工具列表
        """
        tools = [
            await self.example_tool(),
            await self.github_trending_tool(),
            # 在这里添加更多工具
        ]
        logger.info(f"已加载工具列表: {[tool.name for tool in tools]}")
        return tools
    
    @log_tool_execution(tool_name="invoke_agent")
    async def invoke_agent(self, task: str) -> ResultModel:
        """
        使用 LangChain 的 agent_executor 模式调用工具
        
        Args:
            task: 用户任务描述
        
        Returns:
            agent_executor 的执行结果
            
        Raises:
            Exception: 调用失败
        """
        logger.info(f"开始处理任务: {task}")
        tools = await self.get_tools()
        
        # 使用基础的系统提示
        system_prompt = """你是一个运维代理框架，专门帮助用户处理各种运维和开发相关的任务。

你可以使用以下工具来处理用户的任务：

1. **example_operation**: 示例运维操作工具
   - 用于演示基本的工具调用流程

2. **github_trending_analysis**: GitHub趋势项目分析工具
   - 获取GitHub每日/每周热门趋势项目
   - 分析项目详细信息、适用场景和使用指南
   - 生成结构化报告（Markdown和JSON格式）
   - 支持指定时间周期（每日/每周）和项目数量

**使用示例**：
- "获取今天的GitHub热门项目"
- "分析本周GitHub趋势项目"
- "获取前10个热门开源项目并生成报告"

请根据用户的任务描述，选择合适的工具来完成任务。
如果当前没有合适的工具来处理用户的请求，请告知用户需要添加相应的工具。

请始终以JSON格式返回结果，包含以下字段：
- task: 任务描述
- status: 执行状态 (success/failed)
- result: 执行结果描述
- failed_reason: 失败原因（如果失败的话）
"""
        
        # 创建 agent_executor
        logger.info("创建 agent_executor")
        agent_executor = create_react_agent(
            llm.bind_tools(tools, parallel_tool_calls=False),
            tools=tools,
            response_format=ResultModel
        )
        
        # 执行 agent
        logger.info("开始执行 agent")
        try:
            # 获取Langfuse回调处理器
            callbacks = get_langfuse_callbacks()

            response = await agent_executor.ainvoke(
                input={
                    "messages": [
                        SystemMessage(system_prompt),
                        HumanMessage(task),
                    ]
                },
                config=RunnableConfig(
                    recursion_limit=100,
                    callbacks=callbacks,
                ),
            )
            
            structured_response = response.get("structured_response")
            logger.info(f"任务执行完成，状态: {structured_response.status}")
            return structured_response
        except Exception as e:
            logger.error(f"执行agent时发生错误: {str(e)}", exc_info=True)
            # 创建一个错误响应
            error_response = ResultModel(
                task=task,
                status="failed",
                result="任务执行失败",
                failed_reason=f"执行过程中发生错误: {str(e)}"
            )
            return error_response


# 为了向后兼容，保留旧的类名别名
APIAgent = OpsAgent


async def main_cli():
    """
    命令行交互主函数
    """
    ops = OpsAgent()
    print("欢迎使用OPS Agent基础框架，请输入您的操作，输入'exit'结束程序")
    print("这是一个基础框架，您可以在此基础上添加各种运维代理功能。")

    while True:
        user_input = input("\n请输入任务描述: ")
        if user_input.lower() in ['退出', 'exit', 'quit']:
            print("程序已退出")
            break

        if not user_input.strip():
            print("任务描述不能为空，请重新输入")
            continue

        try:
            result = await ops.invoke_agent(user_input)
            # 格式化输出结果，使其更易读
            print("\n===== 任务执行结果 =====")
            print(f"任务: {result.task}")
            print(f"状态: {result.status}")
            print(f"结果: {result.result}")
            if result.failed_reason:
                print(f"失败原因: {result.failed_reason}")
            print("========================\n")
        except Exception as e:
            print(f"\n任务执行出错: {str(e)}\n")


if __name__ == '__main__':
    import asyncio
    # 交互式模式
    asyncio.run(main_cli())
