# -*- coding: utf-8 -*-

"""
File: github_trending_cli.py
Author: AI Assistant
Date: 2025/8/27

GitHub趋势项目命令行工具
支持立即执行任务和启动定时调度器
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.github_trending_agent import GitHubTrendingAgent
from agents.video_publish_agent import VideoPublishAgent


def print_help():
    """
    打印帮助信息
    """
    print("GitHub趋势项目分析工具")
    print()
    print("用法:")
    print("  python github_trending_cli.py run-daily              # 立即执行每日任务并生成报告")
    print("  python github_trending_cli.py run-weekly             # 立即执行每周任务并生成报告")
    print("  python github_trending_cli.py run-video <daily|weekly> # 生成报告并合成为视频")
    print("  python github_trending_cli.py schedule               # 启动定时调度器")
    print("  python github_trending_cli.py help                   # 显示帮助信息")
    print()
    print("定时调度配置:")
    print("- 每日趋势: 每天 09:00")
    print("- 每周趋势: 每周一 10:00")
    print()
    print("环境变量配置:")
    print("必需:")
    print("- OPENAI_API_KEY: OpenAI API密钥")
    print()
    print("可选:")
    print("- GITHUB_TOKEN: GitHub API token (提高API限制)")
    print("- OPENAI_MODEL: OpenAI模型名称 (默认: gpt-3.5-turbo)")
    print("- OPENAI_BASE_URL: OpenAI API基础URL")
    print()
    print("邮件配置 (可选):")
    print("- EMAIL_SMTP_SERVER: SMTP服务器 (默认: smtp.exmail.qq.com)")
    print("- EMAIL_SMTP_PORT: SMTP端口 (默认: 465)")
    print("- EMAIL_SENDER: 发件人邮箱")
    print("- EMAIL_PASSWORD: 发件人密码或授权码")
    print("- EMAIL_RECEIVERS: 收件人邮箱列表 (逗号分隔)")


async def main():
    """
    主函数
    """
    # 将所有逻辑包裹在 try...except 中以捕获和诊断错误
    try:
        if len(sys.argv) < 2:
            print_help()
            return
        
        command = sys.argv[1].lower()
        
        github_token = os.getenv('GITHUB_TOKEN')
        
        if command == 'run-daily':
            print("立即执行每日趋势任务...")
            agent = GitHubTrendingAgent(github_token)
            await agent.run_once('daily')
            
        elif command == 'run-weekly':
            print("立即执行每周趋势任务...")
            agent = GitHubTrendingAgent(github_token)
            await agent.run_once('weekly')

        elif command == 'run-video':
            if len(sys.argv) < 3 or sys.argv[2].lower() not in ['daily', 'weekly']:
                print("\n错误: 'run-video' 命令需要一个参数: 'daily' 或 'weekly'")
                print_help()
                return

            period = sys.argv[2].lower()
            print(f"--- 开始执行 {period} 报告的视频生成任务 ---")

            # 1. 获取报告数据
            print("\n步骤 1/3: 获取并分析 GitHub 趋势数据...")
            github_agent = GitHubTrendingAgent(github_token)
            repos = github_agent.get_trending_repos(period=period, limit=3)
            if not repos:
                print("未能获取 GitHub 趋势数据，任务中止。\n")
                return
            report_data = await github_agent.generate_report(repos, period)
            if not report_data or not report_data['projects']:
                print("未能生成报告数据，任务中止。\n")
                return
            print("数据分析完成。")

            # 2. 生成视频
            print("\n步骤 2/3: 开始生成视频 (这可能需要几分钟时间)...")
            video_agent = VideoPublishAgent()
            video_path = await video_agent.create_video_from_report(report_data)

            if not video_path:
                # 失败信息已在 agent 内部打印，这里直接退出
                print("\n任务因视频生成失败而中止。")
                return

            print(f"\n步骤 3/3: 视频生成成功！")
            print(f"视频已保存至: {os.path.abspath(video_path)}")
        elif command == 'schedule':
            print("启动定时调度器...")
            agent = GitHubTrendingAgent(github_token)
            agent.start_scheduler()
            
        elif command == 'help':
            print_help()
            
        else:
            print(f"未知命令: {command}")
            print_help()

    except Exception as e:
        print("\n----------------------------------------")
        print("【程序运行出错】")
        error_message = str(e)

        # 诊断常见错误
        if "ffmpeg" in error_message.lower() or ("No such file or directory" in error_message and "ffmpeg" in error_message):
            print("【诊断结果】: 缺少 'FFMPEG' 程序。")
            print("【解决方案】: MoviePy 需要 FFMPEG 来处理视频。请访问 https://ffmpeg.org/download.html 下载并安装它，然后确保其路径已添加到系统的环境变量(PATH)中。")
        
        elif ("font" in error_message.lower() or ".ttf" in error_message.lower() or ".ttc" in error_message.lower()) and ("No such file or directory" in error_message or "cannot find" in error_message):
            print("【诊断结果】: 找不到指定的字体文件。")
            print(f"【解决方案】: 代码需要一个中文字体。请检查路径 '{os.path.abspath('C:/Windows/Fonts/msyh.ttc')}' 是否正确，或者您可以将代码中的字体路径修改为您电脑上已安装的任何一个中文字体文件路径。")

        elif "OPENAI_API_KEY" in error_message or "AuthenticationError" in str(type(e)):
            print("【诊断结果】: OpenAI API 密钥配置错误。")
            print("【解决方案】: 请检查项目根目录下的 `.env` 文件，确保 `OPENAI_API_KEY=sk-xxxx...` 已被正确设置。")

        elif "Connection refused" in error_message or "timed out" in error_message:
            print("【诊断结果】: 网络连接问题。")
            print("【解决方案】: 无法连接到网络服务（可能是 OpenAI 或 GitHub）。请检查您的网络连接、代理设置，并确保相关服务可用。")

        else:
            print("【诊断结果】: 发生了一个未知错误。")
            print("【详细信息】: ", error_message)
        
        print("----------------------------------------\n")


if __name__ == '__main__':
    asyncio.run(main())
