# -*- coding: utf-8 -*-

"""
File: github_trending_cli.py
Author: AI Assistant
Date: 2025/8/27

GitHub趋势项目命令行工具
支持立即执行任务和启动定时调度器
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.github_trending_agent import GitHubTrendingAgent


def print_help():
    """
    打印帮助信息
    """
    print("GitHub趋势项目分析工具")
    print()
    print("用法:")
    print("  python github_trending_cli.py run-daily     # 立即执行每日任务")
    print("  python github_trending_cli.py run-weekly    # 立即执行每周任务")
    print("  python github_trending_cli.py schedule      # 启动定时调度器")
    print("  python github_trending_cli.py help          # 显示帮助信息")
    print()
    print("定时调度配置:")
    print("- 每日趋势: 每天 09:00")
    print("- 每周趋势: 每周一 10:00")
    print()
    print("环境变量配置:")
    print("必需:")
    print("- OPENAI_API_KEY: OpenAI API密钥")
    print()
    print("可选:")
    print("- GITHUB_TOKEN: GitHub API token (提高API限制)")
    print("- OPENAI_MODEL: OpenAI模型名称 (默认: gpt-3.5-turbo)")
    print("- OPENAI_BASE_URL: OpenAI API基础URL")
    print()
    print("邮件配置 (可选):")
    print("- EMAIL_SMTP_SERVER: SMTP服务器 (默认: smtp.exmail.qq.com)")
    print("- EMAIL_SMTP_PORT: SMTP端口 (默认: 465)")
    print("- EMAIL_SENDER: 发件人邮箱")
    print("- EMAIL_PASSWORD: 发件人密码或授权码")
    print("- EMAIL_RECEIVERS: 收件人邮箱列表 (逗号分隔)")


async def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        print_help()
        return
    
    command = sys.argv[1].lower()
    
    # 初始化GitHub趋势代理
    github_token = os.getenv('GITHUB_TOKEN')
    agent = GitHubTrendingAgent(github_token)
    
    if command == 'run-daily':
        print("立即执行每日趋势任务...")
        await agent.run_once('daily')
        
    elif command == 'run-weekly':
        print("立即执行每周趋势任务...")
        await agent.run_once('weekly')
        
    elif command == 'schedule':
        print("启动定时调度器...")
        agent.start_scheduler()
        
    elif command == 'help':
        print_help()
        
    else:
        print(f"未知命令: {command}")
        print()
        print_help()


if __name__ == '__main__':
    asyncio.run(main())
