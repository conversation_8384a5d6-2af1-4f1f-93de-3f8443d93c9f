# 智能Zabbix Agent

## 🎯 概述

基于LangChain + MCP (Model Context Protocol) 框架的智能Zabbix监控代理，使用ReAct模式进行推理和行动，实现了完全AI驱动的监控数据分析。

## 功能特性

### 🔍 核心功能
- **智能工具选择**: 基于ReAct模式，AI自动选择合适的MCP工具组合
- **真实数据获取**: 集成本地zabbix-mcp-server，获取真实的Zabbix监控数据
- **多维度监控**: 支持主机、主机组、监控项、历史数据、问题告警等全方位监控
- **智能分析报告**: AI驱动的专业监控数据分析和解读

### 🛠️ 技术架构

基于LangChain + MCP的ReAct Agent架构：

```
┌─────────────────────────────────────────────────────────┐
│                ReAct Agent层                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │   Thought       │ │     Action      │ │ Observation │ │
│  │   (推理分析)     │ │   (工具调用)     │ │  (结果观察)  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                LangChain工具层                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │  工具发现与选择  │ │  参数处理与验证  │ │  结果格式化  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                MCP通信层                                 │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│  │  zabbix-mcp     │ │  异步调用处理    │ │  错误处理    │ │
│  │  40+个工具      │ │                 │ │             │ │
│  └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 文件结构

```
agents/zabbix/
├── __init__.py                  # 包初始化文件
├── zabbix_agent_ai.py          # ReAct Agent主类
├── zabbix_mcp_client.py        # MCP客户端
├── zabbix_prompt.md            # 系统提示词
└── README.md                   # 本文档
```

## 核心组件

### 1. ZabbixAgentAI (ReAct Agent主类)

基于LangChain + MCP框架的智能代理：

- **ReAct推理模式**: 使用Thought → Action → Observation循环进行智能分析
- **动态工具发现**: 自动发现并初始化40+个Zabbix MCP工具
- **智能工具选择**: AI根据用户需求自动选择合适的工具组合
- **真实数据获取**: 直接与本地zabbix-mcp-server通信获取真实监控数据

### 2. ZabbixMCPClient (MCP客户端)

基于官方MCP SDK的客户端实现：

- **STDIO通信**: 使用标准输入输出与zabbix-mcp-server通信
- **异步调用处理**: 支持异步MCP工具调用和结果处理
- **错误处理与重试**: 完善的错误处理和超时控制机制
- **工具缓存**: 缓存可用工具列表，提高性能

### 3. 支持的MCP工具

自动发现的40+个Zabbix MCP工具包括：

- **主机管理**: host_get, hostgroup_get, host_create, host_update
- **监控项**: item_get, item_create, item_update, item_delete
- **历史数据**: history_get, trend_get
- **问题告警**: problem_get, trigger_get, event_get
- **模板管理**: template_get, template_create, template_update
- **用户管理**: user_get, user_create, user_update
- **维护管理**: maintenance_get, maintenance_create
- **图形管理**: graph_get, discoveryrule_get

## 环境配置

### zabbix-mcp-server配置

ZabbixAgent需要本地运行的zabbix-mcp-server来获取真实的Zabbix数据：

```bash
# 1. 复制配置文件
copy "mcp-server\zabbix-mcp-server\config\.env.example" "mcp-server\zabbix-mcp-server\config\.env"

# 2. 编辑 .env 文件，配置以下变量：
ZABBIX_URL=http://your-zabbix-server/api_jsonrpc.php
ZABBIX_TOKEN=your-api-token
# 或者使用用户名密码
# ZABBIX_USER=your-username
# ZABBIX_PASSWORD=your-password

# 3. 启动zabbix-mcp-server
cd mcp-server/zabbix-mcp-server
uv run python src/zabbix_mcp_server.py
```

### LangChain和OpenAI配置

```bash
# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key

# Langfuse追踪配置（可选）
LANGFUSE_PUBLIC_KEY=your-langfuse-public-key
LANGFUSE_SECRET_KEY=your-langfuse-secret-key
LANGFUSE_HOST=https://langfuse.inhand.online
```

## 使用示例

### 1. 启动zabbix-mcp-server

首先需要启动本地的zabbix-mcp-server：

```bash
# 在第一个终端启动zabbix-mcp-server
cd mcp-server/zabbix-mcp-server
uv run python src/zabbix_mcp_server.py
```

### 2. 通过OpsAgent使用（推荐）

```bash
# 在第二个终端启动ops-agent
uv run python ops_agent.py

# 输入Zabbix相关任务
获取zabbix平台上所有主机群组
查看ics-c-master-136主机的CPU使用率
分析Web服务器主机组的性能状况
```

### 3. 直接使用ZabbixAgentAI

```python
from agents.zabbix.zabbix_agent_ai import ZabbixAgentAI

# 初始化ReAct Agent
agent = ZabbixAgentAI()

# 执行监控分析任务
result = await agent.process_task("获取zabbix平台上所有主机群组")
print(result["message"])
```

### 4. ReAct推理过程示例

```
用户输入: 查看ics-c-master-136主机的CPU使用率

Thought: 我需要获取指定主机的CPU使用率数据，首先要找到主机，然后获取CPU监控项，最后获取历史数据。

Action: host_get
Action Input: {"filter": {"host": ["ics-c-master-136"]}, "output": "extend"}
Observation: [{"hostid": "10614", "host": "ics-c-master-136", "name": "ics-c-master-136"}]

Thought: 现在我有了主机ID，需要获取CPU使用率监控项。

Action: item_get
Action Input: {"hostids": ["10614"], "search": {"key_": "system.cpu.util"}, "output": "extend"}
Observation: [{"itemid": "78861", "key_": "system.cpu.util", "name": "CPU使用率"}]

Thought: 现在我有了监控项ID，可以获取最新的CPU使用率数据。

Action: history_get
Action Input: {"itemids": ["78861"], "limit": 1, "sortfield": "clock", "sortorder": "DESC"}
Observation: [{"clock": "1631049600", "value": "23.6"}]

Final Answer: ics-c-master-136主机的当前CPU使用率是23.6%。
```

### 5. 支持的查询示例

- "获取zabbix平台上所有主机群组"
- "查看ics-c-master-136主机的详细信息"
- "查看ics-c-master-136主机的CPU使用率"
- "分析Web服务器主机组的性能状况"
- "获取数据库服务器组最近24小时的监控报告"
- "检查Linux服务器组的CPU和内存使用情况"
- "获取最近的问题和告警信息"

## ReAct Agent特性

### 🧠 智能推理能力

- **任务理解**: AI自动分析用户需求，理解监控任务的具体要求
- **工具选择**: 从40+个MCP工具中智能选择最合适的工具组合
- **步骤规划**: 自动规划多步骤执行流程，处理数据依赖关系
- **结果分析**: 对获取的监控数据进行专业分析和解读

### 🔧 MCP工具集成

- **动态发现**: 自动发现zabbix-mcp-server提供的所有可用工具
- **参数处理**: 智能处理工具参数，支持JSON格式和自然语言输入
- **异步调用**: 支持异步MCP工具调用，提高执行效率
- **错误处理**: 完善的错误处理和重试机制

### 📊 数据处理能力

- **真实数据**: 直接从Zabbix服务器获取真实的监控数据
- **格式化输出**: 自动格式化JSON数据，提供可读性强的结果
- **数据验证**: 验证数据完整性和格式正确性
- **结果缓存**: 支持中间结果缓存，优化多步骤执行

## 输出格式

ZabbixAgent基于ReAct模式，提供详细的推理过程和结果：

### 推理过程输出
```
Question: 用户的问题
Thought: AI的推理分析
Action: 选择的MCP工具
Action Input: 工具参数
Observation: 工具返回的数据
... (可重复多次)
Final Answer: 最终分析结果
```

### 数据格式
- **JSON格式**: 完整的结构化数据，包含所有字段和值
- **专业分析**: AI对数据的专业解读和分析
- **可读性强**: 格式化的输出，便于理解和使用

### 示例输出
```json
{
  "success": true,
  "message": "主机群组详细信息如下：\n- 主机群组ID：1，名称：Templates...",
  "data": {
    "input": "获取zabbix平台上所有主机群组",
    "output": "AI的完整分析结果",
    "intermediate_steps": [...]
  }
}
```

## 故障排除

### 常见问题

1. **zabbix-mcp-server连接失败**
   - 确认zabbix-mcp-server正在运行：`uv run python src/zabbix_mcp_server.py`
   - 检查Zabbix服务器配置：ZABBIX_URL, ZABBIX_TOKEN
   - 验证网络连接和防火墙设置

2. **MCP工具调用失败**
   - 检查工具参数格式（JSON格式）
   - 验证Zabbix用户权限
   - 确认主机组和监控项存在

3. **ReAct Agent执行失败**
   - 检查OpenAI API配置和额度
   - 验证LangChain版本兼容性
   - 查看详细错误日志

4. **异步调用超时**
   - 检查网络延迟
   - 调整超时设置（默认30秒）
   - 确认Zabbix服务器响应正常

### 调试模式

启用详细日志：

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)

# 启用ReAct Agent详细输出
agent_executor = AgentExecutor(verbose=True, ...)
```

## 扩展开发

### 添加新的MCP工具支持

1. 确保zabbix-mcp-server支持新的工具
2. ZabbixAgentAI会自动发现新工具
3. 更新ReAct提示词以包含新工具的使用说明

### 自定义ReAct提示

1. 修改`_create_react_agent`方法中的提示模板
2. 添加特定领域的推理指导
3. 优化工具选择和参数处理逻辑

### 集成其他MCP服务器

1. 在`_initialize_mcp_tools`中添加新的MCP客户端
2. 实现对应的工具调用接口
3. 更新工具发现和选择逻辑

## 版本信息

- **版本**: 2.0.0
- **架构**: LangChain + MCP ReAct Agent
- **更新日期**: 2025/7/17
- **主要依赖**:
  - LangChain (ReAct Agent框架)
  - OpenAI GPT-4 (推理引擎)
  - zabbix-mcp-server (数据源)
  - Langfuse (可选，用于追踪)

## 许可证

本项目遵循与ops-agent主项目相同的许可证。