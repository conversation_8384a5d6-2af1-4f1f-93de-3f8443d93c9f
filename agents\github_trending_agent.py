# -*- coding: utf-8 -*-

"""
File: github_trending_agent.py
Author: AI Assistant
Date: 2025/8/27

GitHub Trending Agent - 获取GitHub热门趋势项目
定时获取GitHub上最热门的项目，进行详细分析并生成报告。
"""

import requests
from bs4 import BeautifulSoup
import base64
import datetime
import os
import json
import schedule
import time
import asyncio
from typing import Dict, List, Optional
from utils.logging_config import logger
from utils.email_sender import email_sender


class GitHubTrendingAgent:
    """
    GitHub趋势项目获取代理
    
    功能：
    - 获取每日/每周GitHub热门项目
    - 分析项目详细信息
    - 生成简单易懂的项目描述
    - 提取使用指南
    - 输出结构化报告
    """
    
    def __init__(self, github_token: Optional[str] = None):
        """
        初始化GitHub趋势代理

        Args:
            github_token: GitHub API token (可选，用于提高API限制)
        """
        self.github_token = github_token
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        # GitHub API专用headers
        self.api_headers = {
            'User-Agent': 'GitHub-Trending-Agent/1.0',
            'Accept': 'application/vnd.github.v3+json'
        }
        if github_token:
            self.api_headers['Authorization'] = f'token {github_token}'

        # 设置session以复用连接
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        logger.info("GitHub趋势代理初始化完成")
    
    def get_trending_repos(self, period: str = 'daily', limit: int = 10) -> List[Dict]:
        """
        获取GitHub趋势项目

        Args:
            period: 时间周期 ('daily' 或 'weekly')
            limit: 获取项目数量限制

        Returns:
            项目列表
        """
        url = 'https://github.com/trending'
        if period == 'weekly':
            url += '?since=weekly'

        logger.info(f"获取{period}趋势项目，URL: {url}")

        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试第{attempt + 1}次请求...")

                # 使用session发送请求
                response = self.session.get(url, timeout=30)
                response.raise_for_status()

                logger.info(f"成功获取页面，状态码: {response.status_code}")

                soup = BeautifulSoup(response.text, 'html.parser')

                repos = []
                # 查找项目容器 - 尝试多种可能的选择器
                articles = soup.find_all('article', class_='Box-row')
                if not articles:
                    # 尝试其他可能的选择器
                    articles = soup.find_all('div', class_='Box-row')

                if not articles:
                    logger.warning("未找到项目容器，尝试解析整个页面")
                    # 如果找不到标准容器，尝试查找仓库链接
                    repo_links = soup.find_all('a', href=lambda h: h and h.startswith('/') and '/' in h[1:])
                    articles = []
                    for link in repo_links[:limit]:
                        if link.get('href', '').count('/') == 2:  # 格式: /owner/repo
                            articles.append(link.parent.parent if link.parent and link.parent.parent else link.parent)

                articles = articles[:limit]
                logger.info(f"找到{len(articles)}个项目容器")

                for i, article in enumerate(articles):
                    try:
                        # 提取项目名称 - 多种方式尝试
                        repo_name = None

                        # 方式1: 标准的h2标签
                        h2_tag = article.find('h2', class_='h3')
                        if h2_tag:
                            repo_link = h2_tag.find('a')
                            if repo_link:
                                repo_name = repo_link.get('href', '').strip('/')

                        # 方式2: 直接查找仓库链接
                        if not repo_name:
                            repo_link = article.find('a', href=lambda h: h and h.startswith('/') and h.count('/') == 2)
                            if repo_link:
                                repo_name = repo_link.get('href', '').strip('/')

                        if not repo_name:
                            logger.warning(f"第{i+1}个项目无法提取名称，跳过")
                            continue

                        # 提取描述
                        description = 'No description'
                        desc_selectors = [
                            'p.col-9',
                            'p[class*="color-fg-muted"]',
                            'p.mb-1',
                            'p'
                        ]
                        for selector in desc_selectors:
                            desc_tag = article.select_one(selector)
                            if desc_tag and desc_tag.text.strip():
                                description = desc_tag.text.strip()
                                break

                        # 提取编程语言
                        language = 'Unknown'
                        lang_tag = article.find('span', itemprop='programmingLanguage')
                        if not lang_tag:
                            # 尝试其他方式查找语言
                            lang_spans = article.find_all('span')
                            for span in lang_spans:
                                if span.get('style') and 'background-color' in span.get('style', ''):
                                    next_span = span.find_next_sibling('span')
                                    if next_span:
                                        language = next_span.text.strip()
                                        break
                        else:
                            language = lang_tag.text.strip()

                        # 提取星标数
                        total_stars = '0'
                        star_link = article.find('a', href=lambda h: h and 'stargazers' in h)
                        if star_link:
                            total_stars = star_link.text.strip()

                        # 提取周期内新增星标
                        period_stars = '0'
                        star_spans = article.find_all('span')
                        for span in star_spans:
                            span_text = span.text.strip()
                            if 'stars' in span_text.lower() or '★' in span_text:
                                # 提取数字
                                import re
                                numbers = re.findall(r'[\d,]+', span_text)
                                if numbers:
                                    period_stars = numbers[0]
                                    break

                        repo_url = f'https://github.com/{repo_name}'

                        repos.append({
                            'name': repo_name,
                            'description': description,
                            'language': language,
                            'total_stars': total_stars,
                            'period_stars': period_stars,
                            'url': repo_url
                        })

                        logger.info(f"成功解析项目: {repo_name}")

                    except Exception as e:
                        logger.warning(f"解析第{i+1}个项目信息时出错: {e}")
                        continue

                if repos:
                    logger.info(f"成功获取{len(repos)}个趋势项目")
                    return repos
                else:
                    logger.warning("未能解析到任何项目，可能页面结构已变化")
                    if attempt < max_retries - 1:
                        logger.info("等待5秒后重试...")
                        time.sleep(5)
                        continue
                    else:
                        return []

            except requests.exceptions.RequestException as e:
                logger.error(f"第{attempt + 1}次请求失败: {e}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5
                    logger.info(f"等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error("所有重试都失败了")
                    return []
            except Exception as e:
                logger.error(f"获取趋势项目时发生未知错误: {e}")
                if attempt < max_retries - 1:
                    logger.info("等待5秒后重试...")
                    time.sleep(5)
                else:
                    return []

        return []
    
    def get_repo_readme(self, owner: str, repo: str) -> str:
        """
        获取仓库README内容
        
        Args:
            owner: 仓库所有者
            repo: 仓库名称
            
        Returns:
            README内容
        """
        try:
            api_url = f'https://api.github.com/repos/{owner}/{repo}/readme'
            response = requests.get(api_url, headers=self.headers)
            
            if response.status_code == 200:
                content_data = response.json()
                content = base64.b64decode(content_data['content']).decode('utf-8')
                return content
            else:
                logger.warning(f"无法获取{owner}/{repo}的README: {response.status_code}")
                return 'README not found or API limit reached.'
                
        except Exception as e:
            logger.error(f"获取README失败: {e}")
            return 'Error fetching README.'
    
    def extract_detailed_description(self, readme: str) -> str:
        """
        从README中提取详细描述
        
        Args:
            readme: README内容
            
        Returns:
            详细描述
        """
        if not readme or readme in ['README not found or API limit reached.', 'Error fetching README.']:
            return 'No detailed description available.'
        
        # 查找常见的描述部分
        sections = ['## Overview', '## Introduction', '## Description', '## About']
        
        for section in sections:
            if section in readme:
                start = readme.find(section)
                # 查找下一个二级标题
                next_section = readme.find('##', start + len(section))
                if next_section > 0:
                    content = readme[start:next_section]
                else:
                    content = readme[start:start+800]
                
                # 清理内容
                content = content.replace(section, '').strip()
                return content[:500] + '...' if len(content) > 500 else content
        
        # 如果没有找到特定部分，返回前500字符
        lines = readme.split('\n')
        content = []
        for line in lines[1:]:  # 跳过标题
            if line.strip() and not line.startswith('#'):
                content.append(line.strip())
            if len('\n'.join(content)) > 500:
                break
        
        result = '\n'.join(content)
        return result[:500] + '...' if len(result) > 500 else result
    
    def extract_usage_guide(self, readme: str) -> str:
        """
        从README中提取使用指南
        
        Args:
            readme: README内容
            
        Returns:
            使用指南
        """
        if not readme or readme in ['README not found or API limit reached.', 'Error fetching README.']:
            return '请查看仓库README获取使用指南。'
        
        # 查找常见的使用指南部分
        sections = [
            '## Installation', '## Getting Started', '## Quick Start',
            '## Usage', '## How to use', '## Setup'
        ]
        
        for section in sections:
            if section in readme:
                start = readme.find(section)
                # 查找下一个二级标题
                next_section = readme.find('##', start + len(section))
                if next_section > 0:
                    content = readme[start:next_section]
                else:
                    content = readme[start:start+1000]
                
                return content.strip()
        
        return '请查看仓库README获取详细使用指南。'
    
    def infer_use_scenarios(self, description: str, language: str) -> List[str]:
        """
        推断项目适用场景
        
        Args:
            description: 项目描述
            language: 编程语言
            
        Returns:
            适用场景列表
        """
        scenarios = []
        desc_lower = description.lower()
        
        # 基于描述关键词推断
        if any(word in desc_lower for word in ['cloud', 'aws', 'azure', 'gcp']):
            scenarios.append('云基础设施管理和部署')
        
        if any(word in desc_lower for word in ['ai', 'ml', 'llm', 'machine learning', 'artificial intelligence']):
            scenarios.append('AI/机器学习应用开发')
        
        if any(word in desc_lower for word in ['video', 'streaming', 'media']):
            scenarios.append('视频处理和流媒体应用')
        
        if any(word in desc_lower for word in ['web', 'frontend', 'backend', 'api']):
            scenarios.append('Web应用开发')
        
        if any(word in desc_lower for word in ['database', 'db', 'storage']):
            scenarios.append('数据存储和管理')
        
        if any(word in desc_lower for word in ['security', 'auth', 'encryption']):
            scenarios.append('安全和认证系统')
        
        if any(word in desc_lower for word in ['monitoring', 'logging', 'observability']):
            scenarios.append('系统监控和可观测性')
        
        # 基于编程语言推断
        if language == 'TypeScript' or language == 'JavaScript':
            scenarios.append('前端和Node.js应用开发')
        elif language == 'Python':
            scenarios.append('数据科学、自动化脚本和后端开发')
        elif language == 'Go':
            scenarios.append('高性能后端服务和系统工具')
        elif language == 'Rust':
            scenarios.append('系统级编程和高性能应用')
        elif language == 'Java':
            scenarios.append('企业级应用和大型系统开发')
        
        return scenarios if scenarios else ['通用软件开发和系统集成']

    def generate_simple_description(self, description: str, detailed_desc: str) -> str:
        """
        生成简单易懂的项目描述

        Args:
            description: 原始描述
            detailed_desc: 详细描述

        Returns:
            简化描述
        """
        if len(description) <= 100:
            return description

        # 如果原始描述太长，尝试从详细描述中提取关键信息
        if detailed_desc and detailed_desc != 'No detailed description available.':
            # 提取第一句话
            sentences = detailed_desc.split('.')
            if sentences:
                first_sentence = sentences[0].strip()
                if len(first_sentence) <= 150:
                    return first_sentence + '.'

        # 截断原始描述
        return description[:100] + '...'

    def generate_report(self, repos: List[Dict], period: str) -> Dict:
        """
        生成趋势项目报告

        Args:
            repos: 项目列表
            period: 时间周期

        Returns:
            报告数据
        """
        now = datetime.datetime.now()
        report_data = {
            'generated_at': now.isoformat(),
            'period': period,
            'total_projects': len(repos),
            'projects': []
        }

        logger.info(f"开始生成{period}趋势报告，共{len(repos)}个项目")

        for i, repo in enumerate(repos, 1):
            try:
                logger.info(f"处理项目 {i}/{len(repos)}: {repo['name']}")

                # 解析仓库名称
                if '/' in repo['name']:
                    owner, repo_name = repo['name'].split('/', 1)
                else:
                    logger.warning(f"无效的仓库名称格式: {repo['name']}")
                    continue

                # 获取README
                readme = self.get_repo_readme(owner, repo_name)

                # 提取详细信息
                detailed_desc = self.extract_detailed_description(readme)
                simple_desc = self.generate_simple_description(repo['description'], detailed_desc)
                usage_guide = self.extract_usage_guide(readme)
                scenarios = self.infer_use_scenarios(repo['description'], repo['language'])

                project_data = {
                    'rank': i,
                    'name': repo['name'],
                    'url': repo['url'],
                    'language': repo['language'],
                    'total_stars': repo['total_stars'],
                    'period_stars': repo['period_stars'],
                    'simple_description': simple_desc,
                    'detailed_description': detailed_desc,
                    'use_scenarios': scenarios,
                    'usage_guide': usage_guide,
                    'original_description': repo['description']
                }

                report_data['projects'].append(project_data)

            except Exception as e:
                logger.error(f"处理项目{repo['name']}时出错: {e}")
                continue

        logger.info(f"报告生成完成，成功处理{len(report_data['projects'])}个项目")
        return report_data

    def save_markdown_report(self, report_data: Dict, output_dir: str = 'data') -> str:
        """
        保存Markdown格式报告

        Args:
            report_data: 报告数据
            output_dir: 输出目录

        Returns:
            报告文件路径
        """
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件名
        timestamp = datetime.datetime.fromisoformat(report_data['generated_at'])
        filename = f"github_trending_{report_data['period']}_{timestamp.strftime('%Y%m%d_%H%M%S')}.md"
        filepath = os.path.join(output_dir, filename)

        # 生成Markdown内容
        content = f"# GitHub {report_data['period'].capitalize()} Trending Top {report_data['total_projects']}\n\n"
        content += f"**生成时间**: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        content += f"**项目总数**: {report_data['total_projects']}\n\n"
        content += "---\n\n"

        for project in report_data['projects']:
            content += f"## {project['rank']}. {project['name']}\n\n"
            content += f"**编程语言**: {project['language']}\n\n"
            content += f"**简单描述**: {project['simple_description']}\n\n"
            content += f"**详细描述**:\n{project['detailed_description']}\n\n"
            content += f"**适用场景**:\n"
            for scenario in project['use_scenarios']:
                content += f"- {scenario}\n"
            content += "\n"
            content += f"**星标统计**: 总计 {project['total_stars']} | {report_data['period']}新增 {project['period_stars']}\n\n"
            content += f"**项目链接**: [{project['name']}]({project['url']})\n\n"
            content += f"**使用指南**:\n```\n{project['usage_guide']}\n```\n\n"
            content += "---\n\n"

        # 保存文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"Markdown报告已保存: {filepath}")
        return filepath

    def save_json_report(self, report_data: Dict, output_dir: str = 'data') -> str:
        """
        保存JSON格式报告

        Args:
            report_data: 报告数据
            output_dir: 输出目录

        Returns:
            报告文件路径
        """
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件名
        timestamp = datetime.datetime.fromisoformat(report_data['generated_at'])
        filename = f"github_trending_{report_data['period']}_{timestamp.strftime('%Y%m%d_%H%M%S')}.json"
        filepath = os.path.join(output_dir, filename)

        # 保存文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)

        logger.info(f"JSON报告已保存: {filepath}")
        return filepath

    async def process_task(self, task: str) -> Dict:
        """
        处理GitHub趋势分析任务

        Args:
            task: 任务描述

        Returns:
            处理结果
        """
        try:
            logger.info(f"开始处理GitHub趋势任务: {task}")

            # 解析任务参数
            period = 'daily'
            limit = 10

            if 'weekly' in task.lower() or '每周' in task or '周' in task:
                period = 'weekly'

            if 'daily' in task.lower() or '每日' in task or '天' in task:
                period = 'daily'

            # 提取数量限制
            import re
            numbers = re.findall(r'\d+', task)
            if numbers:
                limit = min(int(numbers[0]), 20)  # 最多20个项目

            # 获取趋势项目
            repos = self.get_trending_repos(period, limit)

            if not repos:
                return {
                    "status": "failed",
                    "message": "未能获取到趋势项目数据"
                }

            # 生成报告
            report_data = self.generate_report(repos, period)

            # 保存报告
            markdown_path = self.save_markdown_report(report_data)
            json_path = self.save_json_report(report_data)

            # 发送邮件
            email_sent = await self.send_email_report(report_data, [markdown_path, json_path])

            result_message = f"成功获取并分析了{len(report_data['projects'])}个{period}趋势项目。\n"
            result_message += f"Markdown报告: {markdown_path}\n"
            result_message += f"JSON报告: {json_path}\n"

            if email_sent:
                result_message += "邮件报告已发送\n"
            else:
                result_message += "邮件发送失败，请检查邮件配置\n"

            result_message += "\n项目概览:\n"

            for project in report_data['projects'][:5]:  # 显示前5个项目
                result_message += f"{project['rank']}. {project['name']} ({project['language']}) - {project['simple_description']}\n"

            if len(report_data['projects']) > 5:
                result_message += f"... 还有{len(report_data['projects']) - 5}个项目，详见报告文件。"

            return {
                "status": "success",
                "message": result_message,
                "data": {
                    "markdown_report": markdown_path,
                    "json_report": json_path,
                    "project_count": len(report_data['projects']),
                    "period": period
                }
            }

        except Exception as e:
            logger.error(f"处理GitHub趋势任务失败: {e}")
            return {
                "status": "failed",
                "message": f"处理任务时发生错误: {str(e)}"
            }

    async def send_email_report(self, report_data: Dict, attachments: List[str] = None) -> bool:
        """
        发送邮件报告

        Args:
            report_data: 报告数据
            attachments: 附件文件路径列表

        Returns:
            发送是否成功
        """
        try:
            # 生成邮件主题
            timestamp = datetime.datetime.fromisoformat(report_data['generated_at'])
            subject = f"GitHub {report_data['period'].capitalize()} Trending Report - {timestamp.strftime('%Y-%m-%d')}"

            # 生成HTML邮件正文
            html_body = email_sender.create_trending_email_body(report_data)

            # 发送邮件
            success = await email_sender.send_email_async(
                subject=subject,
                body=html_body,
                is_html=True,
                attachments=attachments
            )

            return success

        except Exception as e:
            logger.error(f"发送邮件报告失败: {e}")
            return False

    async def run_daily_task(self):
        """
        执行每日趋势任务
        """
        try:
            logger.info("开始执行每日GitHub趋势任务")
            result = await self.process_task("获取每日热门项目前10个")

            if result.get("status") == "success":
                logger.info(f"每日趋势任务执行成功")
            else:
                logger.error(f"每日趋势任务执行失败: {result.get('message')}")

        except Exception as e:
            logger.error(f"每日趋势任务执行异常: {e}")

    async def run_weekly_task(self):
        """
        执行每周趋势任务
        """
        try:
            logger.info("开始执行每周GitHub趋势任务")
            result = await self.process_task("获取每周热门项目前10个")

            if result.get("status") == "success":
                logger.info(f"每周趋势任务执行成功")
            else:
                logger.error(f"每周趋势任务执行失败: {result.get('message')}")

        except Exception as e:
            logger.error(f"每周趋势任务执行异常: {e}")

    def daily_job(self):
        """
        每日任务包装器
        """
        asyncio.run(self.run_daily_task())

    def weekly_job(self):
        """
        每周任务包装器
        """
        asyncio.run(self.run_weekly_task())

    def start_scheduler(self):
        """
        启动定时调度器
        """
        # 配置定时任务
        # 每天上午9点执行每日趋势任务
        schedule.every().day.at("09:00").do(self.daily_job)

        # 每周一上午10点执行每周趋势任务
        schedule.every().monday.at("10:00").do(self.weekly_job)

        logger.info("GitHub趋势定时任务已配置:")
        logger.info("- 每日趋势: 每天 09:00")
        logger.info("- 每周趋势: 每周一 10:00")

        print("GitHub趋势定时调度器已启动")
        print("定时任务配置:")
        print("- 每日趋势: 每天 09:00")
        print("- 每周趋势: 每周一 10:00")
        print("按 Ctrl+C 停止调度器")

        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logger.info("GitHub趋势定时调度器已停止")
            print("\nGitHub趋势定时调度器已停止")

    async def run_once(self, period: str = 'daily'):
        """
        立即执行一次任务（用于测试）

        Args:
            period: 'daily' 或 'weekly'
        """
        task = f"获取{period}热门项目前10个"
        result = await self.process_task(task)

        if result.get("status") == "success":
            print(f"任务执行成功!")
            print(result.get('message'))
        else:
            print(f"任务执行失败: {result.get('message')}")

        return result
