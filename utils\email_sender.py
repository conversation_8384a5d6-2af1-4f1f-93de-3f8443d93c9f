# -*- coding: utf-8 -*-

"""
File: email_sender.py
Author: AI Assistant
Date: 2025/8/27

邮件发送工具
支持发送HTML和附件邮件
"""

import os
import smtplib
import asyncio
import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Optional
from dotenv import load_dotenv
from utils.logging_config import logger

load_dotenv()


class EmailSender:
    """
    邮件发送器
    """
    
    def __init__(self):
        """
        初始化邮件发送器，从环境变量读取配置
        """
        self.smtp_server = os.getenv('EMAIL_SMTP_SERVER', 'smtp.exmail.qq.com')
        self.smtp_port = int(os.getenv('EMAIL_SMTP_PORT', '465'))
        self.sender_email = os.getenv('EMAIL_SENDER')
        self.sender_password = os.getenv('EMAIL_PASSWORD')
        self.receiver_emails = os.getenv('EMAIL_RECEIVERS', '').split(',')
        
        # 清理收件人列表
        self.receiver_emails = [email.strip() for email in self.receiver_emails if email.strip()]
        
        if not self.sender_email or not self.sender_password:
            logger.warning("邮件配置不完整，请检查环境变量 EMAIL_SENDER 和 EMAIL_PASSWORD")
        
        logger.info(f"邮件发送器初始化完成，SMTP: {self.smtp_server}:{self.smtp_port}")
    
    def create_message(self, 
                      subject: str, 
                      body: str, 
                      is_html: bool = False,
                      attachments: Optional[List[str]] = None) -> MIMEMultipart:
        """
        创建邮件消息
        
        Args:
            subject: 邮件主题
            body: 邮件正文
            is_html: 是否为HTML格式
            attachments: 附件文件路径列表
            
        Returns:
            邮件消息对象
        """
        msg = MIMEMultipart()
        msg['From'] = self.sender_email
        msg['To'] = ', '.join(self.receiver_emails)
        msg['Subject'] = subject
        
        # 添加正文
        if is_html:
            msg.attach(MIMEText(body, 'html', 'utf-8'))
        else:
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
        
        # 添加附件
        if attachments:
            for file_path in attachments:
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'rb') as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(file_path)}'
                        )
                        msg.attach(part)
                        logger.info(f"添加附件: {file_path}")
                    except Exception as e:
                        logger.error(f"添加附件失败 {file_path}: {e}")
                else:
                    logger.warning(f"附件文件不存在: {file_path}")
        
        return msg
    
    async def send_email_async(self, 
                              subject: str, 
                              body: str, 
                              is_html: bool = False,
                              attachments: Optional[List[str]] = None) -> bool:
        """
        异步发送邮件
        
        Args:
            subject: 邮件主题
            body: 邮件正文
            is_html: 是否为HTML格式
            attachments: 附件文件路径列表
            
        Returns:
            发送是否成功
        """
        if not self.sender_email or not self.sender_password:
            logger.error("邮件配置不完整，无法发送邮件")
            return False
        
        if not self.receiver_emails:
            logger.error("没有配置收件人，无法发送邮件")
            return False
        
        try:
            msg = self.create_message(subject, body, is_html, attachments)
            
            # 使用SSL连接
            if self.smtp_port == 465:
                await aiosmtplib.send(
                    msg,
                    hostname=self.smtp_server,
                    port=self.smtp_port,
                    username=self.sender_email,
                    password=self.sender_password,
                    use_tls=True
                )
            else:
                # 使用STARTTLS
                await aiosmtplib.send(
                    msg,
                    hostname=self.smtp_server,
                    port=self.smtp_port,
                    username=self.sender_email,
                    password=self.sender_password,
                    start_tls=True
                )
            
            logger.info(f"邮件发送成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            return False
    
    def send_email_sync(self, 
                       subject: str, 
                       body: str, 
                       is_html: bool = False,
                       attachments: Optional[List[str]] = None) -> bool:
        """
        同步发送邮件
        
        Args:
            subject: 邮件主题
            body: 邮件正文
            is_html: 是否为HTML格式
            attachments: 附件文件路径列表
            
        Returns:
            发送是否成功
        """
        if not self.sender_email or not self.sender_password:
            logger.error("邮件配置不完整，无法发送邮件")
            return False
        
        if not self.receiver_emails:
            logger.error("没有配置收件人，无法发送邮件")
            return False
        
        try:
            msg = self.create_message(subject, body, is_html, attachments)
            
            # 使用SSL连接
            if self.smtp_port == 465:
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                server.starttls()
            
            server.login(self.sender_email, self.sender_password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"邮件发送成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            return False
    
    def create_trending_email_body(self, report_data: dict) -> str:
        """
        创建GitHub趋势报告邮件正文
        
        Args:
            report_data: 报告数据
            
        Returns:
            HTML格式的邮件正文
        """
        html_body = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .header {{ background-color: #f4f4f4; padding: 20px; text-align: center; }}
                .project {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
                .project-title {{ color: #0366d6; font-size: 18px; font-weight: bold; }}
                .project-meta {{ color: #666; font-size: 14px; margin: 5px 0; }}
                .project-desc {{ margin: 10px 0; }}
                .scenarios {{ background-color: #f8f9fa; padding: 10px; border-radius: 3px; }}
                .usage {{ background-color: #f6f8fa; padding: 10px; border-radius: 3px; font-family: monospace; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>GitHub {report_data['period'].capitalize()} Trending Top {report_data['total_projects']}</h1>
                <p>生成时间: {report_data['generated_at']}</p>
            </div>
        """
        
        for project in report_data['projects']:
            html_body += f"""
            <div class="project">
                <div class="project-title">
                    {project['rank']}. <a href="{project['url']}" target="_blank">{project['name']}</a>
                </div>
                <div class="project-meta">
                    编程语言: {project['language']} | 
                    总星标: {project['total_stars']} | 
                    {report_data['period']}新增: {project['period_stars']}
                </div>
                <div class="project-desc">
                    <strong>项目描述:</strong> {project['description']}
                </div>
                <div class="scenarios">
                    <strong>适用场景:</strong>
                    <ul>
            """
            
            for scenario in project['use_scenarios']:
                html_body += f"<li>{scenario}</li>"
            
            html_body += f"""
                    </ul>
                </div>
                <div class="usage">
                    <strong>使用指南:</strong><br>
                    {project['usage_guide'].replace('\n', '<br>')}
                </div>
            </div>
            """
        
        html_body += """
            <div class="footer">
                <p>此报告由 GitHub 趋势分析系统自动生成</p>
            </div>
        </body>
        </html>
        """
        
        return html_body


# 创建全局邮件发送器实例
email_sender = EmailSender()
